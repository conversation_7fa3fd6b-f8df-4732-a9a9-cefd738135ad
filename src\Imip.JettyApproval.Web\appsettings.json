{"App": {"SelfUrl": "https://localhost:44321", "HealthCheckUrl": "/health-status"}, "ConnectionStrings": {"Default": "Server=localhost;Database=JETTY_APPROVAL;User ID=sa;Password=*********;Integrated Security=false;TrustServerCertificate=true;Encrypt=true;MultipleActiveResultSets=false;"}, "Seq": {"ServerUrl": "http://localhost:5341", "ApiKey": "2FafZip5QIvfaUzxx0QZ"}, "AuthServer": {"Authority": "https://identity.imip.co.id", "RequireHttpsMetadata": true, "ClientId": "JettyApprovalLocal", "ClientSecret": "eJOOQKnfxK88qRkCkiBEhXkUTvMD94mD", "CertificatePassPhrase": "401340ab-ea38-4a9d-bce5-46e3d3e6b137"}, "OpenIdConnect": {"Authority": "https://identity.imip.co.id", "ClientId": "JettyApprovalLocal", "ClientSecret": "eJOOQKnfxK88qRkCkiBEhXkUTvMD94mD", "RequireHttpsMetadata": true, "ResponseType": "code", "ResponseMode": "query", "UsePkce": true, "SaveTokens": true, "GetClaimsFromUserInfoEndpoint": true, "Scopes": ["openid", "profile", "email"], "PostLogoutRedirectUri": "https://localhost:44321/signout-callback-oidc", "SignedOutRedirectUri": "https://localhost:44321"}, "ExternalAuth": {"ApiUrl": "http://***************/api/common/RequestAuthenticationToken", "Enabled": true}, "StringEncryption": {"DefaultPassPhrase": "5uOIYyxxaxv32bzK"}}