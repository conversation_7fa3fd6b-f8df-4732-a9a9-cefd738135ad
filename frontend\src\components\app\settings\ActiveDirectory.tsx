'use client'

import { putApiActiveDirectorySettings, type PutApiActiveDirectorySettingsData, type ActiveDirectorySettingsDto } from "@/client"
import { useActiveDirectory } from "@/lib/hooks/useActiveDirectory"
import { useToast } from "@/hooks/use-toast"
import { useQueryClient } from "@tanstack/react-query"
import { type SyntheticEvent, useCallback, useEffect, useId, useState } from "react"
import { useForm } from "react-hook-form"
import { QueryNames } from "@/lib/hooks/QueryConstants"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { postApiActiveDirectorySettingsTestConnection } from "@/client"
import { FormField } from "@/components/ui/FormField"
import { EyeIcon, EyeOffIcon } from "lucide-react"

export const ActiveDirectory = () => {
  const { toast } = useToast()
  const { data } = useActiveDirectory()
  const queryClient = useQueryClient()
  const { handleSubmit, register } = useForm()

  const [adSettingDto, setAdSettingDto] = useState<ActiveDirectorySettingsDto | undefined>(data)
  const id = useId()
  const [isVisible, setIsVisible] = useState<boolean>(false)

  const toggleVisibility = () => setIsVisible((prevState) => !prevState)

  useEffect(() => {
    if (data) {
      setAdSettingDto({ ...data })
    }
  }, [data])

  const onChangeEvent = useCallback(
    (e: SyntheticEvent) => {
      const { value, name } = e.target as HTMLInputElement

      if (adSettingDto) {
        setAdSettingDto({ ...adSettingDto, [name]: value })
      }
    },
    [adSettingDto]
  )

  const onCheckboxChange = useCallback(
    (name: string, checked: boolean) => {
      if (adSettingDto) {
        setAdSettingDto({
          ...adSettingDto,
          [name]: checked,
        })
      }
    },
    [adSettingDto]
  )

  const onSubmitEvent = async () => {
    try {
      await putApiActiveDirectorySettings({
        body: adSettingDto,
      } as PutApiActiveDirectorySettingsData)
      toast({
        title: 'Success',
        description: 'Active Directory settings updated successfully',
        variant: 'default',
      })
      void queryClient.invalidateQueries({ queryKey: [QueryNames.GetActiveDirectory] })
    } catch (err: unknown) {
      if (err instanceof Error) {
        toast({
          title: 'Failed',
          description: "Active Directory settings update wasn't successful.",
          variant: 'destructive',
        })
      }
    }
  }

  const testConnection = async () => {
    try {
      await postApiActiveDirectorySettingsTestConnection({
        body: adSettingDto,
      })
      toast({
        title: 'Success',
        description: 'Connection to Active Directory successful',
        variant: 'default',
      })
    } catch (err: unknown) {
      if (err instanceof Error) {
        toast({
          title: 'Failed',
          description: "Connection to Active Directory failed",
          variant: 'destructive',
        })
      }
    }
  }

  return (
    <section className="p-5 xl:p-10">
      <h3 className="font-medium text-xl">Active Directory</h3>
      <hr className="mt-2 border" />
      <div className="pt-5">
        <form onSubmit={handleSubmit(onSubmitEvent)}>
          <div className="mb-5 space-y-5">
            <FormField
              label="Enable Active Directory"
              description=""
            >
              <div className={cn('flex items-center space-x-2')}>
                <Checkbox
                  id="enabled"
                  checked={adSettingDto?.enabled}
                  onCheckedChange={(checked) => onCheckboxChange('enabled', !!checked)}
                />
                <label htmlFor="enabled" className="font-medium leading-none text-sm">
                  Enable Active Directory
                </label>
              </div>
            </FormField>

            <FormField
              label="Domain"
              description=""
            >
              <Input
                type="text"
                {...register('domain')}
                required
                placeholder="Domain"
                value={adSettingDto?.domain ?? ''}
                onChange={onChangeEvent}
              />
            </FormField>

            <FormField
              label="LDAP Server"
              description=""
            >
              <Input
                type="text"
                {...register('ldapServer')}
                required
                placeholder="LDAP Server"
                value={adSettingDto?.ldapServer ?? ''}
                onChange={onChangeEvent}
              />
            </FormField>

            <FormField
              label="Base DN"
              description=""
            >
              <Input
                type="text"
                {...register('baseDn')}
                placeholder="Base DN"
                value={adSettingDto?.baseDn ?? ''}
                onChange={onChangeEvent}
              />
            </FormField>


            <FormField
              label="Username"
              description=""
            >
              <Input
                type="text"
                {...register('username')}
                required
                placeholder="Username"
                value={adSettingDto?.username ?? ''}
                onChange={onChangeEvent}
              />
            </FormField>

            <FormField
              label="Password"
              description=""
            >
              <div className="relative">
                <Input
                  id={id}
                  type={isVisible ? "text" : "password"}
                  {...register('password')}
                  required
                  placeholder="Password"
                  className="pe-9"
                  value={adSettingDto?.password ?? ''}
                  onChange={onChangeEvent}
                />
                <button
                  className="text-muted-foreground/80 hover:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 absolute inset-y-0 end-0 flex h-full w-9 items-center justify-center rounded-e-md transition-[color,box-shadow] outline-none focus:z-10 focus-visible:ring-[3px] disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50"
                  type="button"
                  onClick={toggleVisibility}
                  aria-label={isVisible ? "Hide password" : "Show password"}
                  aria-pressed={isVisible}
                  aria-controls="password"
                >
                  {isVisible ? (
                    <EyeOffIcon size={16} aria-hidden="true" />
                  ) : (
                    <EyeIcon size={16} aria-hidden="true" />
                  )}
                </button>
              </div>
            </FormField>


            <FormField
              label="port"
              description=""
            >
              <Input
                type="text"
                {...register('port')}
                required
                placeholder="Port"
                value={adSettingDto?.port ?? ''}
                onChange={onChangeEvent}
              />
            </FormField>

            <FormField
              label="useSsl"
              description=""
            >
              <div className={cn('flex items-center space-x-2')}>
                <Checkbox
                  id="useSsl"
                  checked={adSettingDto?.useSsl}
                  onCheckedChange={(checked) => onCheckboxChange('useSsl', !!checked)}
                />
                <label htmlFor="useSsl" className="font-medium leading-none text-sm">
                  Use SSL
                </label>
              </div>
            </FormField>

            <FormField
              label="autoLogin"
              description=""
            >
              <div className={cn('flex items-center space-x-2')}>
                <Checkbox
                  id="autoLogin"
                  checked={adSettingDto?.autoLogin}
                  onCheckedChange={(checked) => onCheckboxChange('autoLogin', !!checked)}
                />
                <label htmlFor="autoLogin" className="font-medium leading-none text-sm">
                  Auto Login
                </label>
              </div>
            </FormField>

            <FormField
              label="Token Secret"
              description=""
            >
              <Input
                type="text"
                {...register('tokenSecret')}
                placeholder="Token Secret"
                value={adSettingDto?.tokenSecret ?? ''}
                onChange={onChangeEvent}
              />
            </FormField>

            <FormField
              label="Default Username"
              description=""
            >
              <Input
                type="text"
                {...register('defaultUsername')}
                placeholder="Default Username"
                value={adSettingDto?.defaultUsername ?? ''}
                onChange={onChangeEvent}
              />
            </FormField>

            <FormField
              label=" Enable Windows Authentication"
              description=""
            >
              <div className={cn('flex items-center space-x-2')}>
                <Checkbox
                  id="windowsAuthEnabled"
                  checked={adSettingDto?.windowsAuthEnabled}
                  onCheckedChange={(checked) => onCheckboxChange('windowsAuthEnabled', !!checked)}
                />
                <label htmlFor="windowsAuthEnabled" className="text-sm font-medium leading-none">
                  Enable Windows Authentication
                </label>
              </div>
            </FormField>
          </div>
          <div className="w-full space-x-5">
            <Button type="submit">Save</Button>
            <Button
              type="button"
              variant="outline"
              onClick={(e) => {
                e.preventDefault()
                void testConnection()
              }}
            >
              Test Connection
            </Button>
          </div>
        </form>
      </div>
    </section>
  )
}
