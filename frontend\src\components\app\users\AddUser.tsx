'use client'
import { type IdentityUserCreateDto, postApiIdentityUsers } from '@/client'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON>alogFooter,
  <PERSON><PERSON>Header,
  <PERSON>alogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { useToast } from '@/lib/useToast'
import { QueryNames } from '@/lib/hooks/QueryConstants'
import { useGrantedPolicies } from '@/lib/hooks/useGrantedPolicies'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { Toaster } from '@/components/ui/toaster'
import { RiAddLine } from '@remixicon/react'
import { FormSection, FormField } from '@/components/ui/FormField'
import { handleApiError } from '@/lib/handleApiError'


export type AddUserProps = {
  children?: React.ReactNode
}

export const AddUser = ({ children }: AddUserProps) => {
  const { can } = useGrantedPolicies()
  const [open, setOpen] = useState(false)
  const [isActive, setIsActive] = useState(true)
  const [lockoutEnabled, setLockoutEnabled] = useState(true)
  const { toast } = useToast()
  const queryClient = useQueryClient()
  const { handleSubmit, register } = useForm<IdentityUserCreateDto>()

  const createUserMutation = useMutation({
    mutationFn: async (userData: IdentityUserCreateDto) =>
      postApiIdentityUsers({ body: userData }),
    onSuccess: () => {
      toast({
        title: 'Success',
        description: 'User Created Successfully',
        variant: 'success',
      })
      void queryClient.invalidateQueries({ queryKey: [QueryNames.GetUsers] })
      setOpen(false)
    },
    onError: (err: unknown) => {
      const error = handleApiError(err);
      toast({
        title: error.title,
        description: error.description,
        variant: 'error',
      })
    }
  })

  const onSubmit = (formData: IdentityUserCreateDto) => {
    // Merge form data with checkbox states
    const userData: IdentityUserCreateDto = {
      ...formData,
      isActive,
      lockoutEnabled
    }

    createUserMutation.mutate(userData)
  }

  return (
    <section>
      <Toaster />
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger asChild>{children}</DialogTrigger>
        <section className="flex items-center justify-between pb-5">
          {can('AbpIdentity.Users.Create') && (
            <Button size='sm' className="w-full sm:py-1 sm:mt-0 sm:w-fit" onClick={() => setOpen(true)}>
              <RiAddLine className="-ml-1 size-4 shrink-0" aria-hidden="true" />
              <span className="hidden truncate sm:inline">Create New User</span>
            </Button>
          )}
        </section>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create a New User</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleSubmit(onSubmit)} className='mt-2'>
            <section className="flex w-full flex-col space-y-2">
              <FormSection>
                <FormField
                  label="Username"
                  description="The username of the user"
                >
                  <Input required {...register('userName')} placeholder="Username" />
                </FormField>
                <FormField
                  label="Password"
                  description="The password of the user"
                >
                  <Input required type="password" {...register('password')} placeholder="Password" />
                </FormField>
                <FormField
                  label="Name"
                  description="The name of the user"
                >
                  <Input required {...register('name')} placeholder="Name" />
                </FormField>
                <FormField
                  label="Surname"
                  description="The surname of the user"
                >
                  <Input required {...register('surname')} placeholder="Surname" />
                </FormField>
                <FormField
                  label="Email"
                  description="The email of the user"
                >
                  <Input required {...register('email')} placeholder="Email" />
                </FormField>
                <FormField
                  label="Phone Number"
                  description="The phone number of the user"
                >
                  <Input required {...register('phoneNumber')} placeholder="Phone Number" />
                </FormField>
                <FormField
                  label="Is Active"
                  description="The active status of the user"
                >
                  <Checkbox
                    id="isActive"
                    name="isActive"
                    defaultChecked
                    checked={isActive}
                    onCheckedChange={(checked) => setIsActive(!!checked.valueOf())}
                  />
                </FormField>
                <FormField
                  label="Lockout Enabled"
                  description="The lockout status of the user"
                >
                  <Checkbox
                    id="lockoutEnabled"
                    name="lockoutEnabled"
                    defaultChecked
                    checked={lockoutEnabled}
                    onCheckedChange={(checked) => setLockoutEnabled(!!checked.valueOf())}
                  />
                </FormField>
              </FormSection>
            </section>
            <DialogFooter className="mt-5">
              <Button
                variant="ghost"
                onClick={(e) => {
                  e.preventDefault()
                  setOpen(false)
                }}
                disabled={createUserMutation.isPending}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={createUserMutation.isPending}>
                {createUserMutation.isPending ? 'Saving...' : 'Save'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </section>
  )
}
