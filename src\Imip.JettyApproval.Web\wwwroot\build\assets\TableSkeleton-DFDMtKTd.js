import{r as P,j as h}from"./vendor-CrSBzUoz.js";import{j as he,k as ve,l as _e,D as Fe,m as xe,A as $e,n as Ve,o as Pe}from"./radix-J8gE3lNP.js";import{G as D,aw as ue}from"./app-layout-cjyge1nB.js";import{I as Me}from"./index.esm-CbbfhPHe.js";import{C as Ie}from"./card-VZZUjpJW.js";/**
   * table-core
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   */function E(e,o){return typeof e=="function"?e(o):e}function V(e,o){return t=>{o.setState(n=>({...n,[e]:E(t,n[e])}))}}function N(e){return e instanceof Function}function ye(e){return Array.isArray(e)&&e.every(o=>typeof o=="number")}function De(e,o){const t=[],n=r=>{r.forEach(i=>{t.push(i);const l=o(i);l!=null&&l.length&&n(l)})};return n(e),t}function S(e,o,t){let n=[],r;return i=>{let l;t.key&&t.debug&&(l=Date.now());const u=e(i);if(!(u.length!==n.length||u.some((c,m)=>n[m]!==c)))return r;n=u;let g;if(t.key&&t.debug&&(g=Date.now()),r=o(...u),t==null||t.onChange==null||t.onChange(r),t.key&&t.debug&&t!=null&&t.debug()){const c=Math.round((Date.now()-l)*100)/100,d=Math.round((Date.now()-g)*100)/100/16,s=(f,p)=>{for(f=String(f);f.length<p;)f=" "+f;return f}}return r}}function w(e,o,t,n){return{debug:()=>{var r;return(r=e?.debugAll)!=null?r:e[o]},key:!1,onChange:n}}function Ee(e,o,t,n){const r=()=>{var l;return(l=i.getValue())!=null?l:e.options.renderFallbackValue},i={id:`${o.id}_${t.id}`,row:o,column:t,getValue:()=>o.getValue(n),renderValue:r,getContext:S(()=>[e,t,o,i],(l,u,a,g)=>({table:l,column:u,row:a,cell:g,getValue:g.getValue,renderValue:g.renderValue}),w(e.options,"debugCells"))};return e._features.forEach(l=>{l.createCell==null||l.createCell(i,t,o,e)},{}),i}function Ae(e,o,t,n){var r,i;const u={...e._getDefaultColumnDef(),...o},a=u.accessorKey;let g=(r=(i=u.id)!=null?i:a?typeof String.prototype.replaceAll=="function"?a.replaceAll(".","_"):a.replace(/\./g,"_"):void 0)!=null?r:typeof u.header=="string"?u.header:void 0,c;if(u.accessorFn?c=u.accessorFn:a&&(a.includes(".")?c=d=>{let s=d;for(const p of a.split(".")){var f;s=(f=s)==null?void 0:f[p]}return s}:c=d=>d[u.accessorKey]),!g)throw new Error;let m={id:`${String(g)}`,accessorFn:c,parent:n,depth:t,columnDef:u,columns:[],getFlatColumns:S(()=>[!0],()=>{var d;return[m,...(d=m.columns)==null?void 0:d.flatMap(s=>s.getFlatColumns())]},w(e.options,"debugColumns")),getLeafColumns:S(()=>[e._getOrderColumnsFn()],d=>{var s;if((s=m.columns)!=null&&s.length){let f=m.columns.flatMap(p=>p.getLeafColumns());return d(f)}return[m]},w(e.options,"debugColumns"))};for(const d of e._features)d.createColumn==null||d.createColumn(m,e);return m}const F="debugHeaders";function le(e,o,t){var n;let i={id:(n=t.id)!=null?n:o.id,column:o,index:t.index,isPlaceholder:!!t.isPlaceholder,placeholderId:t.placeholderId,depth:t.depth,subHeaders:[],colSpan:0,rowSpan:0,headerGroup:null,getLeafHeaders:()=>{const l=[],u=a=>{a.subHeaders&&a.subHeaders.length&&a.subHeaders.map(u),l.push(a)};return u(i),l},getContext:()=>({table:e,header:i,column:o})};return e._features.forEach(l=>{l.createHeader==null||l.createHeader(i,e)}),i}const Ge={createTable:e=>{e.getHeaderGroups=S(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(o,t,n,r)=>{var i,l;const u=(i=n?.map(m=>t.find(d=>d.id===m)).filter(Boolean))!=null?i:[],a=(l=r?.map(m=>t.find(d=>d.id===m)).filter(Boolean))!=null?l:[],g=t.filter(m=>!(n!=null&&n.includes(m.id))&&!(r!=null&&r.includes(m.id)));return L(o,[...u,...g,...a],e)},w(e.options,F)),e.getCenterHeaderGroups=S(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(o,t,n,r)=>(t=t.filter(i=>!(n!=null&&n.includes(i.id))&&!(r!=null&&r.includes(i.id))),L(o,t,e,"center")),w(e.options,F)),e.getLeftHeaderGroups=S(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left],(o,t,n)=>{var r;const i=(r=n?.map(l=>t.find(u=>u.id===l)).filter(Boolean))!=null?r:[];return L(o,i,e,"left")},w(e.options,F)),e.getRightHeaderGroups=S(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.right],(o,t,n)=>{var r;const i=(r=n?.map(l=>t.find(u=>u.id===l)).filter(Boolean))!=null?r:[];return L(o,i,e,"right")},w(e.options,F)),e.getFooterGroups=S(()=>[e.getHeaderGroups()],o=>[...o].reverse(),w(e.options,F)),e.getLeftFooterGroups=S(()=>[e.getLeftHeaderGroups()],o=>[...o].reverse(),w(e.options,F)),e.getCenterFooterGroups=S(()=>[e.getCenterHeaderGroups()],o=>[...o].reverse(),w(e.options,F)),e.getRightFooterGroups=S(()=>[e.getRightHeaderGroups()],o=>[...o].reverse(),w(e.options,F)),e.getFlatHeaders=S(()=>[e.getHeaderGroups()],o=>o.map(t=>t.headers).flat(),w(e.options,F)),e.getLeftFlatHeaders=S(()=>[e.getLeftHeaderGroups()],o=>o.map(t=>t.headers).flat(),w(e.options,F)),e.getCenterFlatHeaders=S(()=>[e.getCenterHeaderGroups()],o=>o.map(t=>t.headers).flat(),w(e.options,F)),e.getRightFlatHeaders=S(()=>[e.getRightHeaderGroups()],o=>o.map(t=>t.headers).flat(),w(e.options,F)),e.getCenterLeafHeaders=S(()=>[e.getCenterFlatHeaders()],o=>o.filter(t=>{var n;return!((n=t.subHeaders)!=null&&n.length)}),w(e.options,F)),e.getLeftLeafHeaders=S(()=>[e.getLeftFlatHeaders()],o=>o.filter(t=>{var n;return!((n=t.subHeaders)!=null&&n.length)}),w(e.options,F)),e.getRightLeafHeaders=S(()=>[e.getRightFlatHeaders()],o=>o.filter(t=>{var n;return!((n=t.subHeaders)!=null&&n.length)}),w(e.options,F)),e.getLeafHeaders=S(()=>[e.getLeftHeaderGroups(),e.getCenterHeaderGroups(),e.getRightHeaderGroups()],(o,t,n)=>{var r,i,l,u,a,g;return[...(r=(i=o[0])==null?void 0:i.headers)!=null?r:[],...(l=(u=t[0])==null?void 0:u.headers)!=null?l:[],...(a=(g=n[0])==null?void 0:g.headers)!=null?a:[]].map(c=>c.getLeafHeaders()).flat()},w(e.options,F))}};function L(e,o,t,n){var r,i;let l=0;const u=function(d,s){s===void 0&&(s=1),l=Math.max(l,s),d.filter(f=>f.getIsVisible()).forEach(f=>{var p;(p=f.columns)!=null&&p.length&&u(f.columns,s+1)},0)};u(e);let a=[];const g=(d,s)=>{const f={depth:s,id:[n,`${s}`].filter(Boolean).join("_"),headers:[]},p=[];d.forEach(C=>{const R=[...p].reverse()[0],v=C.column.depth===f.depth;let _,I=!1;if(v&&C.column.parent?_=C.column.parent:(_=C.column,I=!0),R&&R?.column===_)R.subHeaders.push(C);else{const $=le(t,_,{id:[n,s,_.id,C?.id].filter(Boolean).join("_"),isPlaceholder:I,placeholderId:I?`${p.filter(G=>G.column===_).length}`:void 0,depth:s,index:p.length});$.subHeaders.push(C),p.push($)}f.headers.push(C),C.headerGroup=f}),a.push(f),s>0&&g(p,s-1)},c=o.map((d,s)=>le(t,d,{depth:l,index:s}));g(c,l-1),a.reverse();const m=d=>d.filter(f=>f.column.getIsVisible()).map(f=>{let p=0,C=0,R=[0];f.subHeaders&&f.subHeaders.length?(R=[],m(f.subHeaders).forEach(_=>{let{colSpan:I,rowSpan:$}=_;p+=I,R.push($)})):p=1;const v=Math.min(...R);return C=C+v,f.colSpan=p,f.rowSpan=C,{colSpan:p,rowSpan:C}});return m((r=(i=a[0])==null?void 0:i.headers)!=null?r:[]),a}const b=(e,o,t,n,r,i,l)=>{let u={id:o,index:n,original:t,depth:r,parentId:l,_valuesCache:{},_uniqueValuesCache:{},getValue:a=>{if(u._valuesCache.hasOwnProperty(a))return u._valuesCache[a];const g=e.getColumn(a);if(g!=null&&g.accessorFn)return u._valuesCache[a]=g.accessorFn(u.original,n),u._valuesCache[a]},getUniqueValues:a=>{if(u._uniqueValuesCache.hasOwnProperty(a))return u._uniqueValuesCache[a];const g=e.getColumn(a);if(g!=null&&g.accessorFn)return g.columnDef.getUniqueValues?(u._uniqueValuesCache[a]=g.columnDef.getUniqueValues(u.original,n),u._uniqueValuesCache[a]):(u._uniqueValuesCache[a]=[u.getValue(a)],u._uniqueValuesCache[a])},renderValue:a=>{var g;return(g=u.getValue(a))!=null?g:e.options.renderFallbackValue},subRows:[],getLeafRows:()=>De(u.subRows,a=>a.subRows),getParentRow:()=>u.parentId?e.getRow(u.parentId,!0):void 0,getParentRows:()=>{let a=[],g=u;for(;;){const c=g.getParentRow();if(!c)break;a.push(c),g=c}return a.reverse()},getAllCells:S(()=>[e.getAllLeafColumns()],a=>a.map(g=>Ee(e,u,g,g.id)),w(e.options,"debugRows")),_getAllCellsByColumnId:S(()=>[u.getAllCells()],a=>a.reduce((g,c)=>(g[c.column.id]=c,g),{}),w(e.options,"debugRows"))};for(let a=0;a<e._features.length;a++){const g=e._features[a];g==null||g.createRow==null||g.createRow(u,e)}return u},He={createColumn:(e,o)=>{e._getFacetedRowModel=o.options.getFacetedRowModel&&o.options.getFacetedRowModel(o,e.id),e.getFacetedRowModel=()=>e._getFacetedRowModel?e._getFacetedRowModel():o.getPreFilteredRowModel(),e._getFacetedUniqueValues=o.options.getFacetedUniqueValues&&o.options.getFacetedUniqueValues(o,e.id),e.getFacetedUniqueValues=()=>e._getFacetedUniqueValues?e._getFacetedUniqueValues():new Map,e._getFacetedMinMaxValues=o.options.getFacetedMinMaxValues&&o.options.getFacetedMinMaxValues(o,e.id),e.getFacetedMinMaxValues=()=>{if(e._getFacetedMinMaxValues)return e._getFacetedMinMaxValues()}}},ae=(e,o,t)=>{var n,r;const i=t==null||(n=t.toString())==null?void 0:n.toLowerCase();return!!(!((r=e.getValue(o))==null||(r=r.toString())==null||(r=r.toLowerCase())==null)&&r.includes(i))};ae.autoRemove=e=>M(e);const ge=(e,o,t)=>{var n;return!!(!((n=e.getValue(o))==null||(n=n.toString())==null)&&n.includes(t))};ge.autoRemove=e=>M(e);const de=(e,o,t)=>{var n;return((n=e.getValue(o))==null||(n=n.toString())==null?void 0:n.toLowerCase())===t?.toLowerCase()};de.autoRemove=e=>M(e);const ce=(e,o,t)=>{var n;return(n=e.getValue(o))==null?void 0:n.includes(t)};ce.autoRemove=e=>M(e);const fe=(e,o,t)=>!t.some(n=>{var r;return!((r=e.getValue(o))!=null&&r.includes(n))});fe.autoRemove=e=>M(e)||!(e!=null&&e.length);const pe=(e,o,t)=>t.some(n=>{var r;return(r=e.getValue(o))==null?void 0:r.includes(n)});pe.autoRemove=e=>M(e)||!(e!=null&&e.length);const me=(e,o,t)=>e.getValue(o)===t;me.autoRemove=e=>M(e);const Se=(e,o,t)=>e.getValue(o)==t;Se.autoRemove=e=>M(e);const ee=(e,o,t)=>{let[n,r]=t;const i=e.getValue(o);return i>=n&&i<=r};ee.resolveFilterValue=e=>{let[o,t]=e,n=typeof o!="number"?parseFloat(o):o,r=typeof t!="number"?parseFloat(t):t,i=o===null||Number.isNaN(n)?-1/0:n,l=t===null||Number.isNaN(r)?1/0:r;if(i>l){const u=i;i=l,l=u}return[i,l]};ee.autoRemove=e=>M(e)||M(e[0])&&M(e[1]);const y={includesString:ae,includesStringSensitive:ge,equalsString:de,arrIncludes:ce,arrIncludesAll:fe,arrIncludesSome:pe,equals:me,weakEquals:Se,inNumberRange:ee};function M(e){return e==null||e===""}const ze={getDefaultColumnDef:()=>({filterFn:"auto"}),getInitialState:e=>({columnFilters:[],...e}),getDefaultOptions:e=>({onColumnFiltersChange:V("columnFilters",e),filterFromLeafRows:!1,maxLeafRowFilterDepth:100}),createColumn:(e,o)=>{e.getAutoFilterFn=()=>{const t=o.getCoreRowModel().flatRows[0],n=t?.getValue(e.id);return typeof n=="string"?y.includesString:typeof n=="number"?y.inNumberRange:typeof n=="boolean"||n!==null&&typeof n=="object"?y.equals:Array.isArray(n)?y.arrIncludes:y.weakEquals},e.getFilterFn=()=>{var t,n;return N(e.columnDef.filterFn)?e.columnDef.filterFn:e.columnDef.filterFn==="auto"?e.getAutoFilterFn():(t=(n=o.options.filterFns)==null?void 0:n[e.columnDef.filterFn])!=null?t:y[e.columnDef.filterFn]},e.getCanFilter=()=>{var t,n,r;return((t=e.columnDef.enableColumnFilter)!=null?t:!0)&&((n=o.options.enableColumnFilters)!=null?n:!0)&&((r=o.options.enableFilters)!=null?r:!0)&&!!e.accessorFn},e.getIsFiltered=()=>e.getFilterIndex()>-1,e.getFilterValue=()=>{var t;return(t=o.getState().columnFilters)==null||(t=t.find(n=>n.id===e.id))==null?void 0:t.value},e.getFilterIndex=()=>{var t,n;return(t=(n=o.getState().columnFilters)==null?void 0:n.findIndex(r=>r.id===e.id))!=null?t:-1},e.setFilterValue=t=>{o.setColumnFilters(n=>{const r=e.getFilterFn(),i=n?.find(c=>c.id===e.id),l=E(t,i?i.value:void 0);if(se(r,l,e)){var u;return(u=n?.filter(c=>c.id!==e.id))!=null?u:[]}const a={id:e.id,value:l};if(i){var g;return(g=n?.map(c=>c.id===e.id?a:c))!=null?g:[]}return n!=null&&n.length?[...n,a]:[a]})}},createRow:(e,o)=>{e.columnFilters={},e.columnFiltersMeta={}},createTable:e=>{e.setColumnFilters=o=>{const t=e.getAllLeafColumns(),n=r=>{var i;return(i=E(o,r))==null?void 0:i.filter(l=>{const u=t.find(a=>a.id===l.id);if(u){const a=u.getFilterFn();if(se(a,l.value,u))return!1}return!0})};e.options.onColumnFiltersChange==null||e.options.onColumnFiltersChange(n)},e.resetColumnFilters=o=>{var t,n;e.setColumnFilters(o?[]:(t=(n=e.initialState)==null?void 0:n.columnFilters)!=null?t:[])},e.getPreFilteredRowModel=()=>e.getCoreRowModel(),e.getFilteredRowModel=()=>(!e._getFilteredRowModel&&e.options.getFilteredRowModel&&(e._getFilteredRowModel=e.options.getFilteredRowModel(e)),e.options.manualFiltering||!e._getFilteredRowModel?e.getPreFilteredRowModel():e._getFilteredRowModel())}};function se(e,o,t){return(e&&e.autoRemove?e.autoRemove(o,t):!1)||typeof o>"u"||typeof o=="string"&&!o}const Le=(e,o,t)=>t.reduce((n,r)=>{const i=r.getValue(e);return n+(typeof i=="number"?i:0)},0),Oe=(e,o,t)=>{let n;return t.forEach(r=>{const i=r.getValue(e);i!=null&&(n>i||n===void 0&&i>=i)&&(n=i)}),n},je=(e,o,t)=>{let n;return t.forEach(r=>{const i=r.getValue(e);i!=null&&(n<i||n===void 0&&i>=i)&&(n=i)}),n},Ne=(e,o,t)=>{let n,r;return t.forEach(i=>{const l=i.getValue(e);l!=null&&(n===void 0?l>=l&&(n=r=l):(n>l&&(n=l),r<l&&(r=l)))}),[n,r]},Be=(e,o)=>{let t=0,n=0;if(o.forEach(r=>{let i=r.getValue(e);i!=null&&(i=+i)>=i&&(++t,n+=i)}),t)return n/t},Te=(e,o)=>{if(!o.length)return;const t=o.map(i=>i.getValue(e));if(!ye(t))return;if(t.length===1)return t[0];const n=Math.floor(t.length/2),r=t.sort((i,l)=>i-l);return t.length%2!==0?r[n]:(r[n-1]+r[n])/2},ke=(e,o)=>Array.from(new Set(o.map(t=>t.getValue(e))).values()),qe=(e,o)=>new Set(o.map(t=>t.getValue(e))).size,Ue=(e,o)=>o.length,B={sum:Le,min:Oe,max:je,extent:Ne,mean:Be,median:Te,unique:ke,uniqueCount:qe,count:Ue},Xe={getDefaultColumnDef:()=>({aggregatedCell:e=>{var o,t;return(o=(t=e.getValue())==null||t.toString==null?void 0:t.toString())!=null?o:null},aggregationFn:"auto"}),getInitialState:e=>({grouping:[],...e}),getDefaultOptions:e=>({onGroupingChange:V("grouping",e),groupedColumnMode:"reorder"}),createColumn:(e,o)=>{e.toggleGrouping=()=>{o.setGrouping(t=>t!=null&&t.includes(e.id)?t.filter(n=>n!==e.id):[...t??[],e.id])},e.getCanGroup=()=>{var t,n;return((t=e.columnDef.enableGrouping)!=null?t:!0)&&((n=o.options.enableGrouping)!=null?n:!0)&&(!!e.accessorFn||!!e.columnDef.getGroupingValue)},e.getIsGrouped=()=>{var t;return(t=o.getState().grouping)==null?void 0:t.includes(e.id)},e.getGroupedIndex=()=>{var t;return(t=o.getState().grouping)==null?void 0:t.indexOf(e.id)},e.getToggleGroupingHandler=()=>{const t=e.getCanGroup();return()=>{t&&e.toggleGrouping()}},e.getAutoAggregationFn=()=>{const t=o.getCoreRowModel().flatRows[0],n=t?.getValue(e.id);if(typeof n=="number")return B.sum;if(Object.prototype.toString.call(n)==="[object Date]")return B.extent},e.getAggregationFn=()=>{var t,n;if(!e)throw new Error;return N(e.columnDef.aggregationFn)?e.columnDef.aggregationFn:e.columnDef.aggregationFn==="auto"?e.getAutoAggregationFn():(t=(n=o.options.aggregationFns)==null?void 0:n[e.columnDef.aggregationFn])!=null?t:B[e.columnDef.aggregationFn]}},createTable:e=>{e.setGrouping=o=>e.options.onGroupingChange==null?void 0:e.options.onGroupingChange(o),e.resetGrouping=o=>{var t,n;e.setGrouping(o?[]:(t=(n=e.initialState)==null?void 0:n.grouping)!=null?t:[])},e.getPreGroupedRowModel=()=>e.getFilteredRowModel(),e.getGroupedRowModel=()=>(!e._getGroupedRowModel&&e.options.getGroupedRowModel&&(e._getGroupedRowModel=e.options.getGroupedRowModel(e)),e.options.manualGrouping||!e._getGroupedRowModel?e.getPreGroupedRowModel():e._getGroupedRowModel())},createRow:(e,o)=>{e.getIsGrouped=()=>!!e.groupingColumnId,e.getGroupingValue=t=>{if(e._groupingValuesCache.hasOwnProperty(t))return e._groupingValuesCache[t];const n=o.getColumn(t);return n!=null&&n.columnDef.getGroupingValue?(e._groupingValuesCache[t]=n.columnDef.getGroupingValue(e.original),e._groupingValuesCache[t]):e.getValue(t)},e._groupingValuesCache={}},createCell:(e,o,t,n)=>{e.getIsGrouped=()=>o.getIsGrouped()&&o.id===t.groupingColumnId,e.getIsPlaceholder=()=>!e.getIsGrouped()&&o.getIsGrouped(),e.getIsAggregated=()=>{var r;return!e.getIsGrouped()&&!e.getIsPlaceholder()&&!!((r=t.subRows)!=null&&r.length)}}};function Ke(e,o,t){if(!(o!=null&&o.length)||!t)return e;const n=e.filter(i=>!o.includes(i.id));return t==="remove"?n:[...o.map(i=>e.find(l=>l.id===i)).filter(Boolean),...n]}const Je={getInitialState:e=>({columnOrder:[],...e}),getDefaultOptions:e=>({onColumnOrderChange:V("columnOrder",e)}),createColumn:(e,o)=>{e.getIndex=S(t=>[z(o,t)],t=>t.findIndex(n=>n.id===e.id),w(o.options,"debugColumns")),e.getIsFirstColumn=t=>{var n;return((n=z(o,t)[0])==null?void 0:n.id)===e.id},e.getIsLastColumn=t=>{var n;const r=z(o,t);return((n=r[r.length-1])==null?void 0:n.id)===e.id}},createTable:e=>{e.setColumnOrder=o=>e.options.onColumnOrderChange==null?void 0:e.options.onColumnOrderChange(o),e.resetColumnOrder=o=>{var t;e.setColumnOrder(o?[]:(t=e.initialState.columnOrder)!=null?t:[])},e._getOrderColumnsFn=S(()=>[e.getState().columnOrder,e.getState().grouping,e.options.groupedColumnMode],(o,t,n)=>r=>{let i=[];if(!(o!=null&&o.length))i=r;else{const l=[...o],u=[...r];for(;u.length&&l.length;){const a=l.shift(),g=u.findIndex(c=>c.id===a);g>-1&&i.push(u.splice(g,1)[0])}i=[...i,...u]}return Ke(i,t,n)},w(e.options,"debugTable"))}},T=()=>({left:[],right:[]}),Qe={getInitialState:e=>({columnPinning:T(),...e}),getDefaultOptions:e=>({onColumnPinningChange:V("columnPinning",e)}),createColumn:(e,o)=>{e.pin=t=>{const n=e.getLeafColumns().map(r=>r.id).filter(Boolean);o.setColumnPinning(r=>{var i,l;if(t==="right"){var u,a;return{left:((u=r?.left)!=null?u:[]).filter(m=>!(n!=null&&n.includes(m))),right:[...((a=r?.right)!=null?a:[]).filter(m=>!(n!=null&&n.includes(m))),...n]}}if(t==="left"){var g,c;return{left:[...((g=r?.left)!=null?g:[]).filter(m=>!(n!=null&&n.includes(m))),...n],right:((c=r?.right)!=null?c:[]).filter(m=>!(n!=null&&n.includes(m)))}}return{left:((i=r?.left)!=null?i:[]).filter(m=>!(n!=null&&n.includes(m))),right:((l=r?.right)!=null?l:[]).filter(m=>!(n!=null&&n.includes(m)))}})},e.getCanPin=()=>e.getLeafColumns().some(n=>{var r,i,l;return((r=n.columnDef.enablePinning)!=null?r:!0)&&((i=(l=o.options.enableColumnPinning)!=null?l:o.options.enablePinning)!=null?i:!0)}),e.getIsPinned=()=>{const t=e.getLeafColumns().map(u=>u.id),{left:n,right:r}=o.getState().columnPinning,i=t.some(u=>n?.includes(u)),l=t.some(u=>r?.includes(u));return i?"left":l?"right":!1},e.getPinnedIndex=()=>{var t,n;const r=e.getIsPinned();return r?(t=(n=o.getState().columnPinning)==null||(n=n[r])==null?void 0:n.indexOf(e.id))!=null?t:-1:0}},createRow:(e,o)=>{e.getCenterVisibleCells=S(()=>[e._getAllVisibleCells(),o.getState().columnPinning.left,o.getState().columnPinning.right],(t,n,r)=>{const i=[...n??[],...r??[]];return t.filter(l=>!i.includes(l.column.id))},w(o.options,"debugRows")),e.getLeftVisibleCells=S(()=>[e._getAllVisibleCells(),o.getState().columnPinning.left],(t,n)=>(n??[]).map(i=>t.find(l=>l.column.id===i)).filter(Boolean).map(i=>({...i,position:"left"})),w(o.options,"debugRows")),e.getRightVisibleCells=S(()=>[e._getAllVisibleCells(),o.getState().columnPinning.right],(t,n)=>(n??[]).map(i=>t.find(l=>l.column.id===i)).filter(Boolean).map(i=>({...i,position:"right"})),w(o.options,"debugRows"))},createTable:e=>{e.setColumnPinning=o=>e.options.onColumnPinningChange==null?void 0:e.options.onColumnPinningChange(o),e.resetColumnPinning=o=>{var t,n;return e.setColumnPinning(o?T():(t=(n=e.initialState)==null?void 0:n.columnPinning)!=null?t:T())},e.getIsSomeColumnsPinned=o=>{var t;const n=e.getState().columnPinning;if(!o){var r,i;return!!((r=n.left)!=null&&r.length||(i=n.right)!=null&&i.length)}return!!((t=n[o])!=null&&t.length)},e.getLeftLeafColumns=S(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left],(o,t)=>(t??[]).map(n=>o.find(r=>r.id===n)).filter(Boolean),w(e.options,"debugColumns")),e.getRightLeafColumns=S(()=>[e.getAllLeafColumns(),e.getState().columnPinning.right],(o,t)=>(t??[]).map(n=>o.find(r=>r.id===n)).filter(Boolean),w(e.options,"debugColumns")),e.getCenterLeafColumns=S(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(o,t,n)=>{const r=[...t??[],...n??[]];return o.filter(i=>!r.includes(i.id))},w(e.options,"debugColumns"))}};function We(e){return e||(typeof document<"u"?document:null)}const O={size:150,minSize:20,maxSize:Number.MAX_SAFE_INTEGER},k=()=>({startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,isResizingColumn:!1,columnSizingStart:[]}),Ye={getDefaultColumnDef:()=>O,getInitialState:e=>({columnSizing:{},columnSizingInfo:k(),...e}),getDefaultOptions:e=>({columnResizeMode:"onEnd",columnResizeDirection:"ltr",onColumnSizingChange:V("columnSizing",e),onColumnSizingInfoChange:V("columnSizingInfo",e)}),createColumn:(e,o)=>{e.getSize=()=>{var t,n,r;const i=o.getState().columnSizing[e.id];return Math.min(Math.max((t=e.columnDef.minSize)!=null?t:O.minSize,(n=i??e.columnDef.size)!=null?n:O.size),(r=e.columnDef.maxSize)!=null?r:O.maxSize)},e.getStart=S(t=>[t,z(o,t),o.getState().columnSizing],(t,n)=>n.slice(0,e.getIndex(t)).reduce((r,i)=>r+i.getSize(),0),w(o.options,"debugColumns")),e.getAfter=S(t=>[t,z(o,t),o.getState().columnSizing],(t,n)=>n.slice(e.getIndex(t)+1).reduce((r,i)=>r+i.getSize(),0),w(o.options,"debugColumns")),e.resetSize=()=>{o.setColumnSizing(t=>{let{[e.id]:n,...r}=t;return r})},e.getCanResize=()=>{var t,n;return((t=e.columnDef.enableResizing)!=null?t:!0)&&((n=o.options.enableColumnResizing)!=null?n:!0)},e.getIsResizing=()=>o.getState().columnSizingInfo.isResizingColumn===e.id},createHeader:(e,o)=>{e.getSize=()=>{let t=0;const n=r=>{if(r.subHeaders.length)r.subHeaders.forEach(n);else{var i;t+=(i=r.column.getSize())!=null?i:0}};return n(e),t},e.getStart=()=>{if(e.index>0){const t=e.headerGroup.headers[e.index-1];return t.getStart()+t.getSize()}return 0},e.getResizeHandler=t=>{const n=o.getColumn(e.column.id),r=n?.getCanResize();return i=>{if(!n||!r||(i.persist==null||i.persist(),q(i)&&i.touches&&i.touches.length>1))return;const l=e.getSize(),u=e?e.getLeafHeaders().map(R=>[R.column.id,R.column.getSize()]):[[n.id,n.getSize()]],a=q(i)?Math.round(i.touches[0].clientX):i.clientX,g={},c=(R,v)=>{typeof v=="number"&&(o.setColumnSizingInfo(_=>{var I,$;const G=o.options.columnResizeDirection==="rtl"?-1:1,oe=(v-((I=_?.startOffset)!=null?I:0))*G,re=Math.max(oe/(($=_?.startSize)!=null?$:0),-.999999);return _.columnSizingStart.forEach(Ce=>{let[Re,ie]=Ce;g[Re]=Math.round(Math.max(ie+ie*re,0)*100)/100}),{..._,deltaOffset:oe,deltaPercentage:re}}),(o.options.columnResizeMode==="onChange"||R==="end")&&o.setColumnSizing(_=>({..._,...g})))},m=R=>c("move",R),d=R=>{c("end",R),o.setColumnSizingInfo(v=>({...v,isResizingColumn:!1,startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,columnSizingStart:[]}))},s=We(t),f={moveHandler:R=>m(R.clientX),upHandler:R=>{s?.removeEventListener("mousemove",f.moveHandler),s?.removeEventListener("mouseup",f.upHandler),d(R.clientX)}},p={moveHandler:R=>(R.cancelable&&(R.preventDefault(),R.stopPropagation()),m(R.touches[0].clientX),!1),upHandler:R=>{var v;s?.removeEventListener("touchmove",p.moveHandler),s?.removeEventListener("touchend",p.upHandler),R.cancelable&&(R.preventDefault(),R.stopPropagation()),d((v=R.touches[0])==null?void 0:v.clientX)}},C=Ze()?{passive:!1}:!1;q(i)?(s?.addEventListener("touchmove",p.moveHandler,C),s?.addEventListener("touchend",p.upHandler,C)):(s?.addEventListener("mousemove",f.moveHandler,C),s?.addEventListener("mouseup",f.upHandler,C)),o.setColumnSizingInfo(R=>({...R,startOffset:a,startSize:l,deltaOffset:0,deltaPercentage:0,columnSizingStart:u,isResizingColumn:n.id}))}}},createTable:e=>{e.setColumnSizing=o=>e.options.onColumnSizingChange==null?void 0:e.options.onColumnSizingChange(o),e.setColumnSizingInfo=o=>e.options.onColumnSizingInfoChange==null?void 0:e.options.onColumnSizingInfoChange(o),e.resetColumnSizing=o=>{var t;e.setColumnSizing(o?{}:(t=e.initialState.columnSizing)!=null?t:{})},e.resetHeaderSizeInfo=o=>{var t;e.setColumnSizingInfo(o?k():(t=e.initialState.columnSizingInfo)!=null?t:k())},e.getTotalSize=()=>{var o,t;return(o=(t=e.getHeaderGroups()[0])==null?void 0:t.headers.reduce((n,r)=>n+r.getSize(),0))!=null?o:0},e.getLeftTotalSize=()=>{var o,t;return(o=(t=e.getLeftHeaderGroups()[0])==null?void 0:t.headers.reduce((n,r)=>n+r.getSize(),0))!=null?o:0},e.getCenterTotalSize=()=>{var o,t;return(o=(t=e.getCenterHeaderGroups()[0])==null?void 0:t.headers.reduce((n,r)=>n+r.getSize(),0))!=null?o:0},e.getRightTotalSize=()=>{var o,t;return(o=(t=e.getRightHeaderGroups()[0])==null?void 0:t.headers.reduce((n,r)=>n+r.getSize(),0))!=null?o:0}}};let j=null;function Ze(){if(typeof j=="boolean")return j;let e=!1;try{const o={get passive(){return e=!0,!1}},t=()=>{};window.addEventListener("test",t,o),window.removeEventListener("test",t)}catch{e=!1}return j=e,j}function q(e){return e.type==="touchstart"}const be={getInitialState:e=>({columnVisibility:{},...e}),getDefaultOptions:e=>({onColumnVisibilityChange:V("columnVisibility",e)}),createColumn:(e,o)=>{e.toggleVisibility=t=>{e.getCanHide()&&o.setColumnVisibility(n=>({...n,[e.id]:t??!e.getIsVisible()}))},e.getIsVisible=()=>{var t,n;const r=e.columns;return(t=r.length?r.some(i=>i.getIsVisible()):(n=o.getState().columnVisibility)==null?void 0:n[e.id])!=null?t:!0},e.getCanHide=()=>{var t,n;return((t=e.columnDef.enableHiding)!=null?t:!0)&&((n=o.options.enableHiding)!=null?n:!0)},e.getToggleVisibilityHandler=()=>t=>{e.toggleVisibility==null||e.toggleVisibility(t.target.checked)}},createRow:(e,o)=>{e._getAllVisibleCells=S(()=>[e.getAllCells(),o.getState().columnVisibility],t=>t.filter(n=>n.column.getIsVisible()),w(o.options,"debugRows")),e.getVisibleCells=S(()=>[e.getLeftVisibleCells(),e.getCenterVisibleCells(),e.getRightVisibleCells()],(t,n,r)=>[...t,...n,...r],w(o.options,"debugRows"))},createTable:e=>{const o=(t,n)=>S(()=>[n(),n().filter(r=>r.getIsVisible()).map(r=>r.id).join("_")],r=>r.filter(i=>i.getIsVisible==null?void 0:i.getIsVisible()),w(e.options,"debugColumns"));e.getVisibleFlatColumns=o("getVisibleFlatColumns",()=>e.getAllFlatColumns()),e.getVisibleLeafColumns=o("getVisibleLeafColumns",()=>e.getAllLeafColumns()),e.getLeftVisibleLeafColumns=o("getLeftVisibleLeafColumns",()=>e.getLeftLeafColumns()),e.getRightVisibleLeafColumns=o("getRightVisibleLeafColumns",()=>e.getRightLeafColumns()),e.getCenterVisibleLeafColumns=o("getCenterVisibleLeafColumns",()=>e.getCenterLeafColumns()),e.setColumnVisibility=t=>e.options.onColumnVisibilityChange==null?void 0:e.options.onColumnVisibilityChange(t),e.resetColumnVisibility=t=>{var n;e.setColumnVisibility(t?{}:(n=e.initialState.columnVisibility)!=null?n:{})},e.toggleAllColumnsVisible=t=>{var n;t=(n=t)!=null?n:!e.getIsAllColumnsVisible(),e.setColumnVisibility(e.getAllLeafColumns().reduce((r,i)=>({...r,[i.id]:t||!(i.getCanHide!=null&&i.getCanHide())}),{}))},e.getIsAllColumnsVisible=()=>!e.getAllLeafColumns().some(t=>!(t.getIsVisible!=null&&t.getIsVisible())),e.getIsSomeColumnsVisible=()=>e.getAllLeafColumns().some(t=>t.getIsVisible==null?void 0:t.getIsVisible()),e.getToggleAllColumnsVisibilityHandler=()=>t=>{var n;e.toggleAllColumnsVisible((n=t.target)==null?void 0:n.checked)}}};function z(e,o){return o?o==="center"?e.getCenterVisibleLeafColumns():o==="left"?e.getLeftVisibleLeafColumns():e.getRightVisibleLeafColumns():e.getVisibleLeafColumns()}const et={createTable:e=>{e._getGlobalFacetedRowModel=e.options.getFacetedRowModel&&e.options.getFacetedRowModel(e,"__global__"),e.getGlobalFacetedRowModel=()=>e.options.manualFiltering||!e._getGlobalFacetedRowModel?e.getPreFilteredRowModel():e._getGlobalFacetedRowModel(),e._getGlobalFacetedUniqueValues=e.options.getFacetedUniqueValues&&e.options.getFacetedUniqueValues(e,"__global__"),e.getGlobalFacetedUniqueValues=()=>e._getGlobalFacetedUniqueValues?e._getGlobalFacetedUniqueValues():new Map,e._getGlobalFacetedMinMaxValues=e.options.getFacetedMinMaxValues&&e.options.getFacetedMinMaxValues(e,"__global__"),e.getGlobalFacetedMinMaxValues=()=>{if(e._getGlobalFacetedMinMaxValues)return e._getGlobalFacetedMinMaxValues()}}},tt={getInitialState:e=>({globalFilter:void 0,...e}),getDefaultOptions:e=>({onGlobalFilterChange:V("globalFilter",e),globalFilterFn:"auto",getColumnCanGlobalFilter:o=>{var t;const n=(t=e.getCoreRowModel().flatRows[0])==null||(t=t._getAllCellsByColumnId()[o.id])==null?void 0:t.getValue();return typeof n=="string"||typeof n=="number"}}),createColumn:(e,o)=>{e.getCanGlobalFilter=()=>{var t,n,r,i;return((t=e.columnDef.enableGlobalFilter)!=null?t:!0)&&((n=o.options.enableGlobalFilter)!=null?n:!0)&&((r=o.options.enableFilters)!=null?r:!0)&&((i=o.options.getColumnCanGlobalFilter==null?void 0:o.options.getColumnCanGlobalFilter(e))!=null?i:!0)&&!!e.accessorFn}},createTable:e=>{e.getGlobalAutoFilterFn=()=>y.includesString,e.getGlobalFilterFn=()=>{var o,t;const{globalFilterFn:n}=e.options;return N(n)?n:n==="auto"?e.getGlobalAutoFilterFn():(o=(t=e.options.filterFns)==null?void 0:t[n])!=null?o:y[n]},e.setGlobalFilter=o=>{e.options.onGlobalFilterChange==null||e.options.onGlobalFilterChange(o)},e.resetGlobalFilter=o=>{e.setGlobalFilter(o?void 0:e.initialState.globalFilter)}}},nt={getInitialState:e=>({expanded:{},...e}),getDefaultOptions:e=>({onExpandedChange:V("expanded",e),paginateExpandedRows:!0}),createTable:e=>{let o=!1,t=!1;e._autoResetExpanded=()=>{var n,r;if(!o){e._queue(()=>{o=!0});return}if((n=(r=e.options.autoResetAll)!=null?r:e.options.autoResetExpanded)!=null?n:!e.options.manualExpanding){if(t)return;t=!0,e._queue(()=>{e.resetExpanded(),t=!1})}},e.setExpanded=n=>e.options.onExpandedChange==null?void 0:e.options.onExpandedChange(n),e.toggleAllRowsExpanded=n=>{n??!e.getIsAllRowsExpanded()?e.setExpanded(!0):e.setExpanded({})},e.resetExpanded=n=>{var r,i;e.setExpanded(n?{}:(r=(i=e.initialState)==null?void 0:i.expanded)!=null?r:{})},e.getCanSomeRowsExpand=()=>e.getPrePaginationRowModel().flatRows.some(n=>n.getCanExpand()),e.getToggleAllRowsExpandedHandler=()=>n=>{n.persist==null||n.persist(),e.toggleAllRowsExpanded()},e.getIsSomeRowsExpanded=()=>{const n=e.getState().expanded;return n===!0||Object.values(n).some(Boolean)},e.getIsAllRowsExpanded=()=>{const n=e.getState().expanded;return typeof n=="boolean"?n===!0:!(!Object.keys(n).length||e.getRowModel().flatRows.some(r=>!r.getIsExpanded()))},e.getExpandedDepth=()=>{let n=0;return(e.getState().expanded===!0?Object.keys(e.getRowModel().rowsById):Object.keys(e.getState().expanded)).forEach(i=>{const l=i.split(".");n=Math.max(n,l.length)}),n},e.getPreExpandedRowModel=()=>e.getSortedRowModel(),e.getExpandedRowModel=()=>(!e._getExpandedRowModel&&e.options.getExpandedRowModel&&(e._getExpandedRowModel=e.options.getExpandedRowModel(e)),e.options.manualExpanding||!e._getExpandedRowModel?e.getPreExpandedRowModel():e._getExpandedRowModel())},createRow:(e,o)=>{e.toggleExpanded=t=>{o.setExpanded(n=>{var r;const i=n===!0?!0:!!(n!=null&&n[e.id]);let l={};if(n===!0?Object.keys(o.getRowModel().rowsById).forEach(u=>{l[u]=!0}):l=n,t=(r=t)!=null?r:!i,!i&&t)return{...l,[e.id]:!0};if(i&&!t){const{[e.id]:u,...a}=l;return a}return n})},e.getIsExpanded=()=>{var t;const n=o.getState().expanded;return!!((t=o.options.getIsRowExpanded==null?void 0:o.options.getIsRowExpanded(e))!=null?t:n===!0||n?.[e.id])},e.getCanExpand=()=>{var t,n,r;return(t=o.options.getRowCanExpand==null?void 0:o.options.getRowCanExpand(e))!=null?t:((n=o.options.enableExpanding)!=null?n:!0)&&!!((r=e.subRows)!=null&&r.length)},e.getIsAllParentsExpanded=()=>{let t=!0,n=e;for(;t&&n.parentId;)n=o.getRow(n.parentId,!0),t=n.getIsExpanded();return t},e.getToggleExpandedHandler=()=>{const t=e.getCanExpand();return()=>{t&&e.toggleExpanded()}}}},J=0,Q=10,U=()=>({pageIndex:J,pageSize:Q}),ot={getInitialState:e=>({...e,pagination:{...U(),...e?.pagination}}),getDefaultOptions:e=>({onPaginationChange:V("pagination",e)}),createTable:e=>{let o=!1,t=!1;e._autoResetPageIndex=()=>{var n,r;if(!o){e._queue(()=>{o=!0});return}if((n=(r=e.options.autoResetAll)!=null?r:e.options.autoResetPageIndex)!=null?n:!e.options.manualPagination){if(t)return;t=!0,e._queue(()=>{e.resetPageIndex(),t=!1})}},e.setPagination=n=>{const r=i=>E(n,i);return e.options.onPaginationChange==null?void 0:e.options.onPaginationChange(r)},e.resetPagination=n=>{var r;e.setPagination(n?U():(r=e.initialState.pagination)!=null?r:U())},e.setPageIndex=n=>{e.setPagination(r=>{let i=E(n,r.pageIndex);const l=typeof e.options.pageCount>"u"||e.options.pageCount===-1?Number.MAX_SAFE_INTEGER:e.options.pageCount-1;return i=Math.max(0,Math.min(i,l)),{...r,pageIndex:i}})},e.resetPageIndex=n=>{var r,i;e.setPageIndex(n?J:(r=(i=e.initialState)==null||(i=i.pagination)==null?void 0:i.pageIndex)!=null?r:J)},e.resetPageSize=n=>{var r,i;e.setPageSize(n?Q:(r=(i=e.initialState)==null||(i=i.pagination)==null?void 0:i.pageSize)!=null?r:Q)},e.setPageSize=n=>{e.setPagination(r=>{const i=Math.max(1,E(n,r.pageSize)),l=r.pageSize*r.pageIndex,u=Math.floor(l/i);return{...r,pageIndex:u,pageSize:i}})},e.setPageCount=n=>e.setPagination(r=>{var i;let l=E(n,(i=e.options.pageCount)!=null?i:-1);return typeof l=="number"&&(l=Math.max(-1,l)),{...r,pageCount:l}}),e.getPageOptions=S(()=>[e.getPageCount()],n=>{let r=[];return n&&n>0&&(r=[...new Array(n)].fill(null).map((i,l)=>l)),r},w(e.options,"debugTable")),e.getCanPreviousPage=()=>e.getState().pagination.pageIndex>0,e.getCanNextPage=()=>{const{pageIndex:n}=e.getState().pagination,r=e.getPageCount();return r===-1?!0:r===0?!1:n<r-1},e.previousPage=()=>e.setPageIndex(n=>n-1),e.nextPage=()=>e.setPageIndex(n=>n+1),e.firstPage=()=>e.setPageIndex(0),e.lastPage=()=>e.setPageIndex(e.getPageCount()-1),e.getPrePaginationRowModel=()=>e.getExpandedRowModel(),e.getPaginationRowModel=()=>(!e._getPaginationRowModel&&e.options.getPaginationRowModel&&(e._getPaginationRowModel=e.options.getPaginationRowModel(e)),e.options.manualPagination||!e._getPaginationRowModel?e.getPrePaginationRowModel():e._getPaginationRowModel()),e.getPageCount=()=>{var n;return(n=e.options.pageCount)!=null?n:Math.ceil(e.getRowCount()/e.getState().pagination.pageSize)},e.getRowCount=()=>{var n;return(n=e.options.rowCount)!=null?n:e.getPrePaginationRowModel().rows.length}}},X=()=>({top:[],bottom:[]}),rt={getInitialState:e=>({rowPinning:X(),...e}),getDefaultOptions:e=>({onRowPinningChange:V("rowPinning",e)}),createRow:(e,o)=>{e.pin=(t,n,r)=>{const i=n?e.getLeafRows().map(a=>{let{id:g}=a;return g}):[],l=r?e.getParentRows().map(a=>{let{id:g}=a;return g}):[],u=new Set([...l,e.id,...i]);o.setRowPinning(a=>{var g,c;if(t==="bottom"){var m,d;return{top:((m=a?.top)!=null?m:[]).filter(p=>!(u!=null&&u.has(p))),bottom:[...((d=a?.bottom)!=null?d:[]).filter(p=>!(u!=null&&u.has(p))),...Array.from(u)]}}if(t==="top"){var s,f;return{top:[...((s=a?.top)!=null?s:[]).filter(p=>!(u!=null&&u.has(p))),...Array.from(u)],bottom:((f=a?.bottom)!=null?f:[]).filter(p=>!(u!=null&&u.has(p)))}}return{top:((g=a?.top)!=null?g:[]).filter(p=>!(u!=null&&u.has(p))),bottom:((c=a?.bottom)!=null?c:[]).filter(p=>!(u!=null&&u.has(p)))}})},e.getCanPin=()=>{var t;const{enableRowPinning:n,enablePinning:r}=o.options;return typeof n=="function"?n(e):(t=n??r)!=null?t:!0},e.getIsPinned=()=>{const t=[e.id],{top:n,bottom:r}=o.getState().rowPinning,i=t.some(u=>n?.includes(u)),l=t.some(u=>r?.includes(u));return i?"top":l?"bottom":!1},e.getPinnedIndex=()=>{var t,n;const r=e.getIsPinned();if(!r)return-1;const i=(t=r==="top"?o.getTopRows():o.getBottomRows())==null?void 0:t.map(l=>{let{id:u}=l;return u});return(n=i?.indexOf(e.id))!=null?n:-1}},createTable:e=>{e.setRowPinning=o=>e.options.onRowPinningChange==null?void 0:e.options.onRowPinningChange(o),e.resetRowPinning=o=>{var t,n;return e.setRowPinning(o?X():(t=(n=e.initialState)==null?void 0:n.rowPinning)!=null?t:X())},e.getIsSomeRowsPinned=o=>{var t;const n=e.getState().rowPinning;if(!o){var r,i;return!!((r=n.top)!=null&&r.length||(i=n.bottom)!=null&&i.length)}return!!((t=n[o])!=null&&t.length)},e._getPinnedRows=(o,t,n)=>{var r;return((r=e.options.keepPinnedRows)==null||r?(t??[]).map(l=>{const u=e.getRow(l,!0);return u.getIsAllParentsExpanded()?u:null}):(t??[]).map(l=>o.find(u=>u.id===l))).filter(Boolean).map(l=>({...l,position:n}))},e.getTopRows=S(()=>[e.getRowModel().rows,e.getState().rowPinning.top],(o,t)=>e._getPinnedRows(o,t,"top"),w(e.options,"debugRows")),e.getBottomRows=S(()=>[e.getRowModel().rows,e.getState().rowPinning.bottom],(o,t)=>e._getPinnedRows(o,t,"bottom"),w(e.options,"debugRows")),e.getCenterRows=S(()=>[e.getRowModel().rows,e.getState().rowPinning.top,e.getState().rowPinning.bottom],(o,t,n)=>{const r=new Set([...t??[],...n??[]]);return o.filter(i=>!r.has(i.id))},w(e.options,"debugRows"))}},it={getInitialState:e=>({rowSelection:{},...e}),getDefaultOptions:e=>({onRowSelectionChange:V("rowSelection",e),enableRowSelection:!0,enableMultiRowSelection:!0,enableSubRowSelection:!0}),createTable:e=>{e.setRowSelection=o=>e.options.onRowSelectionChange==null?void 0:e.options.onRowSelectionChange(o),e.resetRowSelection=o=>{var t;return e.setRowSelection(o?{}:(t=e.initialState.rowSelection)!=null?t:{})},e.toggleAllRowsSelected=o=>{e.setRowSelection(t=>{o=typeof o<"u"?o:!e.getIsAllRowsSelected();const n={...t},r=e.getPreGroupedRowModel().flatRows;return o?r.forEach(i=>{i.getCanSelect()&&(n[i.id]=!0)}):r.forEach(i=>{delete n[i.id]}),n})},e.toggleAllPageRowsSelected=o=>e.setRowSelection(t=>{const n=typeof o<"u"?o:!e.getIsAllPageRowsSelected(),r={...t};return e.getRowModel().rows.forEach(i=>{W(r,i.id,n,!0,e)}),r}),e.getPreSelectedRowModel=()=>e.getCoreRowModel(),e.getSelectedRowModel=S(()=>[e.getState().rowSelection,e.getCoreRowModel()],(o,t)=>Object.keys(o).length?K(e,t):{rows:[],flatRows:[],rowsById:{}},w(e.options,"debugTable")),e.getFilteredSelectedRowModel=S(()=>[e.getState().rowSelection,e.getFilteredRowModel()],(o,t)=>Object.keys(o).length?K(e,t):{rows:[],flatRows:[],rowsById:{}},w(e.options,"debugTable")),e.getGroupedSelectedRowModel=S(()=>[e.getState().rowSelection,e.getSortedRowModel()],(o,t)=>Object.keys(o).length?K(e,t):{rows:[],flatRows:[],rowsById:{}},w(e.options,"debugTable")),e.getIsAllRowsSelected=()=>{const o=e.getFilteredRowModel().flatRows,{rowSelection:t}=e.getState();let n=!!(o.length&&Object.keys(t).length);return n&&o.some(r=>r.getCanSelect()&&!t[r.id])&&(n=!1),n},e.getIsAllPageRowsSelected=()=>{const o=e.getPaginationRowModel().flatRows.filter(r=>r.getCanSelect()),{rowSelection:t}=e.getState();let n=!!o.length;return n&&o.some(r=>!t[r.id])&&(n=!1),n},e.getIsSomeRowsSelected=()=>{var o;const t=Object.keys((o=e.getState().rowSelection)!=null?o:{}).length;return t>0&&t<e.getFilteredRowModel().flatRows.length},e.getIsSomePageRowsSelected=()=>{const o=e.getPaginationRowModel().flatRows;return e.getIsAllPageRowsSelected()?!1:o.filter(t=>t.getCanSelect()).some(t=>t.getIsSelected()||t.getIsSomeSelected())},e.getToggleAllRowsSelectedHandler=()=>o=>{e.toggleAllRowsSelected(o.target.checked)},e.getToggleAllPageRowsSelectedHandler=()=>o=>{e.toggleAllPageRowsSelected(o.target.checked)}},createRow:(e,o)=>{e.toggleSelected=(t,n)=>{const r=e.getIsSelected();o.setRowSelection(i=>{var l;if(t=typeof t<"u"?t:!r,e.getCanSelect()&&r===t)return i;const u={...i};return W(u,e.id,t,(l=n?.selectChildren)!=null?l:!0,o),u})},e.getIsSelected=()=>{const{rowSelection:t}=o.getState();return te(e,t)},e.getIsSomeSelected=()=>{const{rowSelection:t}=o.getState();return Y(e,t)==="some"},e.getIsAllSubRowsSelected=()=>{const{rowSelection:t}=o.getState();return Y(e,t)==="all"},e.getCanSelect=()=>{var t;return typeof o.options.enableRowSelection=="function"?o.options.enableRowSelection(e):(t=o.options.enableRowSelection)!=null?t:!0},e.getCanSelectSubRows=()=>{var t;return typeof o.options.enableSubRowSelection=="function"?o.options.enableSubRowSelection(e):(t=o.options.enableSubRowSelection)!=null?t:!0},e.getCanMultiSelect=()=>{var t;return typeof o.options.enableMultiRowSelection=="function"?o.options.enableMultiRowSelection(e):(t=o.options.enableMultiRowSelection)!=null?t:!0},e.getToggleSelectedHandler=()=>{const t=e.getCanSelect();return n=>{var r;t&&e.toggleSelected((r=n.target)==null?void 0:r.checked)}}}},W=(e,o,t,n,r)=>{var i;const l=r.getRow(o,!0);t?(l.getCanMultiSelect()||Object.keys(e).forEach(u=>delete e[u]),l.getCanSelect()&&(e[o]=!0)):delete e[o],n&&(i=l.subRows)!=null&&i.length&&l.getCanSelectSubRows()&&l.subRows.forEach(u=>W(e,u.id,t,n,r))};function K(e,o){const t=e.getState().rowSelection,n=[],r={},i=function(l,u){return l.map(a=>{var g;const c=te(a,t);if(c&&(n.push(a),r[a.id]=a),(g=a.subRows)!=null&&g.length&&(a={...a,subRows:i(a.subRows)}),c)return a}).filter(Boolean)};return{rows:i(o.rows),flatRows:n,rowsById:r}}function te(e,o){var t;return(t=o[e.id])!=null?t:!1}function Y(e,o,t){var n;if(!((n=e.subRows)!=null&&n.length))return!1;let r=!0,i=!1;return e.subRows.forEach(l=>{if(!(i&&!r)&&(l.getCanSelect()&&(te(l,o)?i=!0:r=!1),l.subRows&&l.subRows.length)){const u=Y(l,o);u==="all"?i=!0:(u==="some"&&(i=!0),r=!1)}}),r?"all":i?"some":!1}const Z=/([0-9]+)/gm,lt=(e,o,t)=>we(A(e.getValue(t)).toLowerCase(),A(o.getValue(t)).toLowerCase()),st=(e,o,t)=>we(A(e.getValue(t)),A(o.getValue(t))),ut=(e,o,t)=>ne(A(e.getValue(t)).toLowerCase(),A(o.getValue(t)).toLowerCase()),at=(e,o,t)=>ne(A(e.getValue(t)),A(o.getValue(t))),gt=(e,o,t)=>{const n=e.getValue(t),r=o.getValue(t);return n>r?1:n<r?-1:0},dt=(e,o,t)=>ne(e.getValue(t),o.getValue(t));function ne(e,o){return e===o?0:e>o?1:-1}function A(e){return typeof e=="number"?isNaN(e)||e===1/0||e===-1/0?"":String(e):typeof e=="string"?e:""}function we(e,o){const t=e.split(Z).filter(Boolean),n=o.split(Z).filter(Boolean);for(;t.length&&n.length;){const r=t.shift(),i=n.shift(),l=parseInt(r,10),u=parseInt(i,10),a=[l,u].sort();if(isNaN(a[0])){if(r>i)return 1;if(i>r)return-1;continue}if(isNaN(a[1]))return isNaN(l)?-1:1;if(l>u)return 1;if(u>l)return-1}return t.length-n.length}const H={alphanumeric:lt,alphanumericCaseSensitive:st,text:ut,textCaseSensitive:at,datetime:gt,basic:dt},ct={getInitialState:e=>({sorting:[],...e}),getDefaultColumnDef:()=>({sortingFn:"auto",sortUndefined:1}),getDefaultOptions:e=>({onSortingChange:V("sorting",e),isMultiSortEvent:o=>o.shiftKey}),createColumn:(e,o)=>{e.getAutoSortingFn=()=>{const t=o.getFilteredRowModel().flatRows.slice(10);let n=!1;for(const r of t){const i=r?.getValue(e.id);if(Object.prototype.toString.call(i)==="[object Date]")return H.datetime;if(typeof i=="string"&&(n=!0,i.split(Z).length>1))return H.alphanumeric}return n?H.text:H.basic},e.getAutoSortDir=()=>{const t=o.getFilteredRowModel().flatRows[0];return typeof t?.getValue(e.id)=="string"?"asc":"desc"},e.getSortingFn=()=>{var t,n;if(!e)throw new Error;return N(e.columnDef.sortingFn)?e.columnDef.sortingFn:e.columnDef.sortingFn==="auto"?e.getAutoSortingFn():(t=(n=o.options.sortingFns)==null?void 0:n[e.columnDef.sortingFn])!=null?t:H[e.columnDef.sortingFn]},e.toggleSorting=(t,n)=>{const r=e.getNextSortingOrder(),i=typeof t<"u"&&t!==null;o.setSorting(l=>{const u=l?.find(s=>s.id===e.id),a=l?.findIndex(s=>s.id===e.id);let g=[],c,m=i?t:r==="desc";if(l!=null&&l.length&&e.getCanMultiSort()&&n?u?c="toggle":c="add":l!=null&&l.length&&a!==l.length-1?c="replace":u?c="toggle":c="replace",c==="toggle"&&(i||r||(c="remove")),c==="add"){var d;g=[...l,{id:e.id,desc:m}],g.splice(0,g.length-((d=o.options.maxMultiSortColCount)!=null?d:Number.MAX_SAFE_INTEGER))}else c==="toggle"?g=l.map(s=>s.id===e.id?{...s,desc:m}:s):c==="remove"?g=l.filter(s=>s.id!==e.id):g=[{id:e.id,desc:m}];return g})},e.getFirstSortDir=()=>{var t,n;return((t=(n=e.columnDef.sortDescFirst)!=null?n:o.options.sortDescFirst)!=null?t:e.getAutoSortDir()==="desc")?"desc":"asc"},e.getNextSortingOrder=t=>{var n,r;const i=e.getFirstSortDir(),l=e.getIsSorted();return l?l!==i&&((n=o.options.enableSortingRemoval)==null||n)&&(!(t&&(r=o.options.enableMultiRemove)!=null)||r)?!1:l==="desc"?"asc":"desc":i},e.getCanSort=()=>{var t,n;return((t=e.columnDef.enableSorting)!=null?t:!0)&&((n=o.options.enableSorting)!=null?n:!0)&&!!e.accessorFn},e.getCanMultiSort=()=>{var t,n;return(t=(n=e.columnDef.enableMultiSort)!=null?n:o.options.enableMultiSort)!=null?t:!!e.accessorFn},e.getIsSorted=()=>{var t;const n=(t=o.getState().sorting)==null?void 0:t.find(r=>r.id===e.id);return n?n.desc?"desc":"asc":!1},e.getSortIndex=()=>{var t,n;return(t=(n=o.getState().sorting)==null?void 0:n.findIndex(r=>r.id===e.id))!=null?t:-1},e.clearSorting=()=>{o.setSorting(t=>t!=null&&t.length?t.filter(n=>n.id!==e.id):[])},e.getToggleSortingHandler=()=>{const t=e.getCanSort();return n=>{t&&(n.persist==null||n.persist(),e.toggleSorting==null||e.toggleSorting(void 0,e.getCanMultiSort()?o.options.isMultiSortEvent==null?void 0:o.options.isMultiSortEvent(n):!1))}}},createTable:e=>{e.setSorting=o=>e.options.onSortingChange==null?void 0:e.options.onSortingChange(o),e.resetSorting=o=>{var t,n;e.setSorting(o?[]:(t=(n=e.initialState)==null?void 0:n.sorting)!=null?t:[])},e.getPreSortedRowModel=()=>e.getGroupedRowModel(),e.getSortedRowModel=()=>(!e._getSortedRowModel&&e.options.getSortedRowModel&&(e._getSortedRowModel=e.options.getSortedRowModel(e)),e.options.manualSorting||!e._getSortedRowModel?e.getPreSortedRowModel():e._getSortedRowModel())}},ft=[Ge,be,Je,Qe,He,ze,et,tt,ct,Xe,nt,ot,rt,it,Ye];function pt(e){var o,t;const n=[...ft,...(o=e._features)!=null?o:[]];let r={_features:n};const i=r._features.reduce((d,s)=>Object.assign(d,s.getDefaultOptions==null?void 0:s.getDefaultOptions(r)),{}),l=d=>r.options.mergeOptions?r.options.mergeOptions(i,d):{...i,...d};let a={...{},...(t=e.initialState)!=null?t:{}};r._features.forEach(d=>{var s;a=(s=d.getInitialState==null?void 0:d.getInitialState(a))!=null?s:a});const g=[];let c=!1;const m={_features:n,options:{...i,...e},initialState:a,_queue:d=>{g.push(d),c||(c=!0,Promise.resolve().then(()=>{for(;g.length;)g.shift()();c=!1}).catch(s=>setTimeout(()=>{throw s})))},reset:()=>{r.setState(r.initialState)},setOptions:d=>{const s=E(d,r.options);r.options=l(s)},getState:()=>r.options.state,setState:d=>{r.options.onStateChange==null||r.options.onStateChange(d)},_getRowId:(d,s,f)=>{var p;return(p=r.options.getRowId==null?void 0:r.options.getRowId(d,s,f))!=null?p:`${f?[f.id,s].join("."):s}`},getCoreRowModel:()=>(r._getCoreRowModel||(r._getCoreRowModel=r.options.getCoreRowModel(r)),r._getCoreRowModel()),getRowModel:()=>r.getPaginationRowModel(),getRow:(d,s)=>{let f=(s?r.getPrePaginationRowModel():r.getRowModel()).rowsById[d];if(!f&&(f=r.getCoreRowModel().rowsById[d],!f))throw new Error;return f},_getDefaultColumnDef:S(()=>[r.options.defaultColumn],d=>{var s;return d=(s=d)!=null?s:{},{header:f=>{const p=f.header.column.columnDef;return p.accessorKey?p.accessorKey:p.accessorFn?p.id:null},cell:f=>{var p,C;return(p=(C=f.renderValue())==null||C.toString==null?void 0:C.toString())!=null?p:null},...r._features.reduce((f,p)=>Object.assign(f,p.getDefaultColumnDef==null?void 0:p.getDefaultColumnDef()),{}),...d}},w(e,"debugColumns")),_getColumnDefs:()=>r.options.columns,getAllColumns:S(()=>[r._getColumnDefs()],d=>{const s=function(f,p,C){return C===void 0&&(C=0),f.map(R=>{const v=Ae(r,R,C,p),_=R;return v.columns=_.columns?s(_.columns,v,C+1):[],v})};return s(d)},w(e,"debugColumns")),getAllFlatColumns:S(()=>[r.getAllColumns()],d=>d.flatMap(s=>s.getFlatColumns()),w(e,"debugColumns")),_getAllFlatColumnsById:S(()=>[r.getAllFlatColumns()],d=>d.reduce((s,f)=>(s[f.id]=f,s),{}),w(e,"debugColumns")),getAllLeafColumns:S(()=>[r.getAllColumns(),r._getOrderColumnsFn()],(d,s)=>{let f=d.flatMap(p=>p.getLeafColumns());return s(f)},w(e,"debugColumns")),getColumn:d=>r._getAllFlatColumnsById()[d]};Object.assign(r,m);for(let d=0;d<r._features.length;d++){const s=r._features[d];s==null||s.createTable==null||s.createTable(r)}return r}function Dt(){return e=>S(()=>[e.options.data],o=>{const t={rows:[],flatRows:[],rowsById:{}},n=function(r,i,l){i===void 0&&(i=0);const u=[];for(let g=0;g<r.length;g++){const c=b(e,e._getRowId(r[g],g,l),r[g],g,i,void 0,l?.id);if(t.flatRows.push(c),t.rowsById[c.id]=c,u.push(c),e.options.getSubRows){var a;c.originalSubRows=e.options.getSubRows(r[g],g),(a=c.originalSubRows)!=null&&a.length&&(c.subRows=n(c.originalSubRows,i+1,c))}}return u};return t.rows=n(o),t},w(e.options,"debugTable","getRowModel",()=>e._autoResetPageIndex()))}function mt(e){const o=[],t=n=>{var r;o.push(n),(r=n.subRows)!=null&&r.length&&n.getIsExpanded()&&n.subRows.forEach(t)};return e.rows.forEach(t),{rows:o,flatRows:e.flatRows,rowsById:e.rowsById}}function St(e,o,t){return t.options.filterFromLeafRows?wt(e,o,t):Ct(e,o,t)}function wt(e,o,t){var n;const r=[],i={},l=(n=t.options.maxLeafRowFilterDepth)!=null?n:100,u=function(a,g){g===void 0&&(g=0);const c=[];for(let d=0;d<a.length;d++){var m;let s=a[d];const f=b(t,s.id,s.original,s.index,s.depth,void 0,s.parentId);if(f.columnFilters=s.columnFilters,(m=s.subRows)!=null&&m.length&&g<l){if(f.subRows=u(s.subRows,g+1),s=f,o(s)&&!f.subRows.length){c.push(s),i[s.id]=s,r.push(s);continue}if(o(s)||f.subRows.length){c.push(s),i[s.id]=s,r.push(s);continue}}else s=f,o(s)&&(c.push(s),i[s.id]=s,r.push(s))}return c};return{rows:u(e),flatRows:r,rowsById:i}}function Ct(e,o,t){var n;const r=[],i={},l=(n=t.options.maxLeafRowFilterDepth)!=null?n:100,u=function(a,g){g===void 0&&(g=0);const c=[];for(let d=0;d<a.length;d++){let s=a[d];if(o(s)){var m;if((m=s.subRows)!=null&&m.length&&g<l){const p=b(t,s.id,s.original,s.index,s.depth,void 0,s.parentId);p.subRows=u(s.subRows,g+1),s=p}c.push(s),r.push(s),i[s.id]=s}}return c};return{rows:u(e),flatRows:r,rowsById:i}}function Et(){return e=>S(()=>[e.getPreFilteredRowModel(),e.getState().columnFilters,e.getState().globalFilter],(o,t,n)=>{if(!o.rows.length||!(t!=null&&t.length)&&!n){for(let d=0;d<o.flatRows.length;d++)o.flatRows[d].columnFilters={},o.flatRows[d].columnFiltersMeta={};return o}const r=[],i=[];(t??[]).forEach(d=>{var s;const f=e.getColumn(d.id);if(!f)return;const p=f.getFilterFn();p&&r.push({id:d.id,filterFn:p,resolvedValue:(s=p.resolveFilterValue==null?void 0:p.resolveFilterValue(d.value))!=null?s:d.value})});const l=(t??[]).map(d=>d.id),u=e.getGlobalFilterFn(),a=e.getAllLeafColumns().filter(d=>d.getCanGlobalFilter());n&&u&&a.length&&(l.push("__global__"),a.forEach(d=>{var s;i.push({id:d.id,filterFn:u,resolvedValue:(s=u.resolveFilterValue==null?void 0:u.resolveFilterValue(n))!=null?s:n})}));let g,c;for(let d=0;d<o.flatRows.length;d++){const s=o.flatRows[d];if(s.columnFilters={},r.length)for(let f=0;f<r.length;f++){g=r[f];const p=g.id;s.columnFilters[p]=g.filterFn(s,p,g.resolvedValue,C=>{s.columnFiltersMeta[p]=C})}if(i.length){for(let f=0;f<i.length;f++){c=i[f];const p=c.id;if(c.filterFn(s,p,c.resolvedValue,C=>{s.columnFiltersMeta[p]=C})){s.columnFilters.__global__=!0;break}}s.columnFilters.__global__!==!0&&(s.columnFilters.__global__=!1)}}const m=d=>{for(let s=0;s<l.length;s++)if(d.columnFilters[l[s]]===!1)return!1;return!0};return St(o.rows,m,e)},w(e.options,"debugTable","getFilteredRowModel",()=>e._autoResetPageIndex()))}function At(e){return o=>S(()=>[o.getState().pagination,o.getPrePaginationRowModel(),o.options.paginateExpandedRows?void 0:o.getState().expanded],(t,n)=>{if(!n.rows.length)return n;const{pageSize:r,pageIndex:i}=t;let{rows:l,flatRows:u,rowsById:a}=n;const g=r*i,c=g+r;l=l.slice(g,c);let m;o.options.paginateExpandedRows?m={rows:l,flatRows:u,rowsById:a}:m=mt({rows:l,flatRows:u,rowsById:a}),m.flatRows=[];const d=s=>{m.flatRows.push(s),s.subRows.length&&s.subRows.forEach(d)};return m.rows.forEach(d),m},w(o.options,"debugTable"))}function Gt(){return e=>S(()=>[e.getState().sorting,e.getPreSortedRowModel()],(o,t)=>{if(!t.rows.length||!(o!=null&&o.length))return t;const n=e.getState().sorting,r=[],i=n.filter(a=>{var g;return(g=e.getColumn(a.id))==null?void 0:g.getCanSort()}),l={};i.forEach(a=>{const g=e.getColumn(a.id);g&&(l[a.id]={sortUndefined:g.columnDef.sortUndefined,invertSorting:g.columnDef.invertSorting,sortingFn:g.getSortingFn()})});const u=a=>{const g=a.map(c=>({...c}));return g.sort((c,m)=>{for(let s=0;s<i.length;s+=1){var d;const f=i[s],p=l[f.id],C=p.sortUndefined,R=(d=f?.desc)!=null?d:!1;let v=0;if(C){const _=c.getValue(f.id),I=m.getValue(f.id),$=_===void 0,G=I===void 0;if($||G){if(C==="first")return $?-1:1;if(C==="last")return $?1:-1;v=$&&G?0:$?C:-C}}if(v===0&&(v=p.sortingFn(c,m,f.id)),v!==0)return R&&(v*=-1),p.invertSorting&&(v*=-1),v}return c.index-m.index}),g.forEach(c=>{var m;r.push(c),(m=c.subRows)!=null&&m.length&&(c.subRows=u(c.subRows))}),g};return{rows:u(t.rows),flatRows:r,rowsById:t.rowsById}},w(e.options,"debugTable","getSortedRowModel",()=>e._autoResetPageIndex()))}/**
   * react-table
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   */function Ht(e,o){return e?Rt(e)?P.createElement(e,o):e:null}function Rt(e){return ht(e)||typeof e=="function"||vt(e)}function ht(e){return typeof e=="function"&&(()=>{const o=Object.getPrototypeOf(e);return o.prototype&&o.prototype.isReactComponent})()}function vt(e){return typeof e=="object"&&typeof e.$$typeof=="symbol"&&["react.memo","react.forward_ref"].includes(e.$$typeof.description)}function zt(e){const o={state:{},onStateChange:()=>{},renderFallbackValue:null,...e},[t]=P.useState(()=>({current:pt(o)})),[n,r]=P.useState(()=>t.current.initialState);return t.current.setOptions(i=>({...i,...e,state:{...n,...e.state},onStateChange:l=>{r(l),e.onStateChange==null||e.onStateChange(l)}})),t.current}function Lt({...e}){return h.jsx(he,{"data-slot":"alert-dialog",...e})}function _t({...e}){return h.jsx(Ve,{"data-slot":"alert-dialog-portal",...e})}function Ft({className:e,...o}){return h.jsx(Pe,{"data-slot":"alert-dialog-overlay",className:D("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...o})}function Ot({className:e,...o}){return h.jsxs(_t,{children:[h.jsx(Ft,{}),h.jsx(ve,{"data-slot":"alert-dialog-content",className:D("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...o})]})}function jt({className:e,...o}){return h.jsx("div",{"data-slot":"alert-dialog-header",className:D("flex flex-col gap-2 text-center sm:text-left",e),...o})}function Nt({className:e,...o}){return h.jsx("div",{"data-slot":"alert-dialog-footer",className:D("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...o})}function Bt({className:e,...o}){return h.jsx(_e,{"data-slot":"alert-dialog-title",className:D("text-lg font-semibold",e),...o})}function Tt({className:e,...o}){return h.jsx(Fe,{"data-slot":"alert-dialog-description",className:D("text-muted-foreground text-sm",e),...o})}function kt({className:e,...o}){return h.jsx($e,{className:D(ue(),e),...o})}function qt({className:e,...o}){return h.jsx(xe,{className:D(ue({variant:"outline"}),e),...o})}const xt=(e,o=800)=>{const[t,n]=P.useState(e);return P.useEffect(()=>{const r=setTimeout(()=>{n(e)},o);return()=>{clearTimeout(r)}},[e,o]),t},$t=({onUpdate:e,value:o})=>{const[t,n]=P.useState(o),r=P.useRef(null),i=xt(t),l=P.useRef(!1),u=P.useCallback(a=>{const g=a.target,{value:c}=g;l.current=!0,n(c)},[]);return P.useEffect(()=>{l.current&&(e(i||""),l.current=!1)},[i,e]),P.useEffect(()=>{o!==t&&!l.current&&n(o),o&&r.current?.focus()},[o,t]),h.jsx("section",{className:"search",children:h.jsx(Me,{ref:r,type:"text",value:t,placeholder:"Search...",onChange:u})})},Ut=P.memo($t);function x({className:e,...o}){return h.jsx("div",{"data-slot":"skeleton",className:D("bg-accent animate-pulse rounded-md",e),...o})}function Xt({rowCount:e=10,columnCount:o=4,hasTitle:t=!0,hasSearch:n=!0,hasFilters:r=!0,hasPagination:i=!0,hasActions:l=!0}){const u=Array.from({length:e},(g,c)=>c),a=Array.from({length:o},(g,c)=>c);return h.jsxs(Ie,{className:"space-y-4 py-4",children:[t&&h.jsxs("div",{className:"flex items-center justify-between mb-6 px-4",children:[h.jsx(x,{className:"h-8 w-48"})," ",l&&h.jsxs("div",{className:"flex space-x-2",children:[h.jsx(x,{className:"h-9 w-24"})," ",h.jsx(x,{className:"h-9 w-24"})," "]})]}),(n||r)&&h.jsxs("div",{className:"flex items-center justify-between mb-4 px-4",children:[n&&h.jsx(x,{className:"h-10 w-64"})," ",r&&h.jsx(x,{className:"h-10 w-32"})," "]}),h.jsxs("div",{className:"flex w-full border-b pb-2 px-4",children:[h.jsx(x,{className:"h-6 w-8 mr-4"})," ",a.map(g=>h.jsx(x,{className:`h-6 ${g===a.length-1?"w-1/6":"w-1/4 mr-4"}`},`header-${g}`))]}),u.map(g=>h.jsxs("div",{className:"flex w-full py-3 border-b px-4",children:[h.jsx(x,{className:"h-5 w-5 mr-4"})," ",a.map(c=>h.jsx(x,{className:`h-5 ${c===a.length-1?"w-1/6":"w-1/4 mr-4"}`},`cell-${g}-${c}`))]},`row-${g}`)),i&&h.jsxs("div",{className:"flex items-center justify-between pt-4 px-4",children:[h.jsx(x,{className:"h-5 w-32"})," ",h.jsxs("div",{className:"flex space-x-1",children:[h.jsx(x,{className:"h-8 w-8"})," ",h.jsx(x,{className:"h-8 w-8"})," ",h.jsx(x,{className:"h-8 w-8"})," ",h.jsx(x,{className:"h-8 w-8"})," "]})]})]})}export{Lt as A,Ut as S,Xt as T,Ot as a,jt as b,Bt as c,Tt as d,Nt as e,qt as f,kt as g,Ht as h,Dt as i,Gt as j,At as k,Et as l,zt as u};
//# sourceMappingURL=TableSkeleton-DFDMtKTd.js.map
