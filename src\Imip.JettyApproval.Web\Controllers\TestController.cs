using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Net.Http;
using System.Threading.Tasks;
using System.Text.Json;

namespace Imip.JettyApproval.Web.Controllers;

[ApiController]
[Route("api/[controller]")]
public class TestController : ControllerBase
{
    private readonly ILogger<TestController> _logger;
    private readonly IHttpClientFactory _httpClientFactory;

    public TestController(ILogger<TestController> logger, IHttpClientFactory httpClientFactory)
    {
        _logger = logger;
        _httpClientFactory = httpClientFactory;
    }

    [HttpGet("identity-server")]
    public async Task<IActionResult> TestIdentityServer()
    {
        try
        {
            var client = _httpClientFactory.CreateClient();
            client.Timeout = TimeSpan.FromSeconds(30);

            _logger.LogInformation("Testing connection to identity server...");

            // Test the discovery document endpoint
            var discoveryUrl = "https://identity.imip.co.id/.well-known/openid_configuration";
            
            _logger.LogInformation("Fetching discovery document from: {Url}", discoveryUrl);
            
            var response = await client.GetAsync(discoveryUrl);
            
            _logger.LogInformation("Response status: {StatusCode}", response.StatusCode);
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                _logger.LogInformation("Discovery document retrieved successfully. Length: {Length}", content.Length);
                
                // Try to parse as JSON to validate
                try
                {
                    var json = JsonDocument.Parse(content);
                    var issuer = json.RootElement.GetProperty("issuer").GetString();
                    var authEndpoint = json.RootElement.GetProperty("authorization_endpoint").GetString();
                    var tokenEndpoint = json.RootElement.GetProperty("token_endpoint").GetString();
                    
                    return Ok(new
                    {
                        Status = "Success",
                        Issuer = issuer,
                        AuthorizationEndpoint = authEndpoint,
                        TokenEndpoint = tokenEndpoint,
                        Message = "Identity server is accessible and responding correctly"
                    });
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to parse discovery document as JSON");
                    return Ok(new
                    {
                        Status = "Warning",
                        Message = "Identity server responded but discovery document is not valid JSON",
                        Content = content.Substring(0, Math.Min(500, content.Length))
                    });
                }
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogError("Failed to fetch discovery document. Status: {StatusCode}, Content: {Content}", 
                    response.StatusCode, errorContent);
                
                return BadRequest(new
                {
                    Status = "Error",
                    StatusCode = response.StatusCode,
                    Message = "Identity server returned error response",
                    Content = errorContent
                });
            }
        }
        catch (TaskCanceledException ex)
        {
            _logger.LogError(ex, "Timeout occurred while testing identity server");
            return BadRequest(new
            {
                Status = "Timeout",
                Message = "Connection to identity server timed out",
                Error = ex.Message
            });
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "HTTP request exception while testing identity server");
            return BadRequest(new
            {
                Status = "NetworkError",
                Message = "Network error occurred while connecting to identity server",
                Error = ex.Message
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error while testing identity server");
            return BadRequest(new
            {
                Status = "UnexpectedError",
                Message = "Unexpected error occurred",
                Error = ex.Message
            });
        }
    }
}
