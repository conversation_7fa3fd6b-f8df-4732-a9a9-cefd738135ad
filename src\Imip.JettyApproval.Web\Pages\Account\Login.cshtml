﻿@page
@model Imip.JettyApproval.Web.Pages.Account.IdentityCustomLoginModel
@using Microsoft.AspNetCore.Mvc.Localization
@using Volo.Abp.Account.Localization
@using Volo.Abp.Account.Settings
@using Volo.Abp.Settings
@inject IHtmlLocalizer<AccountResource> L
@inject ISettingProvider SettingProvider
@{
    Layout = "/Pages/Shared/CustomLayout.cshtml";
    ViewData["Title"] = "Login - IMIP Identity Server";
}

@section styles {
    <script src="/js/tailwind.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    backgroundImage: {
                        "hero-pattern":
                            "linear-gradient(to right bottom, rgba('#7ed56f',0.8), rgba('#28b485',0.8)), url('/img/img1.jpg')",
                    },
                },
            },
        };
    </script>
}

<div class="flex h-screen w-full">
    <!-- Left side - Background with logo and illustration -->
    <div class="hidden md:block md:w-1/2 relative bg-[url('/img/bg-image.png')] bg-cover bg-center">
        @* <div class="absolute inset-0 bg-gradient-to-br from-emerald-500/85 to-teal-600/85 z-[5]"></div> *@

        <!-- Header with logo and title -->
        <div class="absolute top-0 left-0 right-0 p-6 flex items-center justify-between z-20">
            <img src="/img/logo-imip-white.png" alt="PT IMIP logo" class="w-22" />
            <h1 class="text-white text-2xl font-bold">Single Sign-On</h1>
        </div>

        <div class="flex flex-col items-center justify-center h-full relative z-10 p-8">
            <img src="/img/3D-SSO-AUTH.png" alt="Authentication Illustration" class="w-80 max-w-full h-auto mx-auto block mb-8" />
            <div class="w-full flex flex-col items-end">
                <p class="text-white text-right text-xl font-medium mt-8 drop-shadow">"Simplify your day — one login to access everything you need."</p>
                <p class="text-white font-bold text-2xl mt-2 drop-shadow">#TogetherWeCan</p>
            </div>
        </div>
    </div>

    <!-- Right side - Login Form -->
    <div class="w-full md:w-1/2 flex flex-col justify-center items-center p-6 md:p-12 bg-white">
        <div class="w-full max-w-md">
            <!-- Mobile header (visible only on small screens) -->
            <div class="flex md:hidden items-center justify-between mb-8">
                <img src="/img/logo-imip-white.png" alt="PT IMIP logo" width="100"
                    class="bg-[#20bf6b] p-2 rounded-lg" />
                <h2 class="text-xl font-bold text-[#20bf6b]">Single Sign-On</h2>
            </div>

            <h1 class="text-2xl font-bold text-gray-800 mb-2">Sign In</h1>
            <p class="text-gray-600 mb-8">Welcome back! Please enter your details account.</p>

            @* Add Alert Messages *@
            @if (Model.Alerts.Any())
            {
                @foreach (var alert in Model.Alerts)
                {
                    <div class="mb-4 p-4 rounded-lg @(alert.Type == Volo.Abp.AspNetCore.Mvc.UI.Alerts.AlertType.Danger ? "bg-red-100 text-red-700" :
                                                                                                       alert.Type == Volo.Abp.AspNetCore.Mvc.UI.Alerts.AlertType.Warning ? "bg-yellow-100 text-yellow-700" :
                                                                                                       alert.Type == Volo.Abp.AspNetCore.Mvc.UI.Alerts.AlertType.Success ? "bg-green-100 text-green-700" :
                                                                                                       "bg-blue-100 text-blue-700")">
                @alert.Text
            </div>
                        }
            }

            <form method="post" class="space-y-5" id="loginForm" onsubmit="return handleLogin(event)">
                <input type="hidden" name="ReturnUrl" value="@Model.ReturnUrl" />
                <input type="hidden" name="ReturnUrlHash" value="@Model.ReturnUrlHash" />

                <div>
                    <label for="LoginInput_UserNameOrEmailAddress"
                        class="block text-sm font-medium text-gray-700 mb-1">Username</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                        </div>
                        <input
                            class="appearance-none border rounded-md w-full py-2 focus:ring-2 focus:ring-emerald-400 pl-10 pr-3 leading-tight border-gray-300 bg-gray-50 focus:outline-none focus:border-emerald-400 focus:bg-white text-gray-700"
                            type="text" asp-for="LoginInput.UserNameOrEmailAddress"
                            placeholder="Enter your username or email" autofocus />
                    </div>
                    <span asp-validation-for="LoginInput.UserNameOrEmailAddress" class="text-red-500 text-sm"></span>
                </div>

                <div>
                    <label for="LoginInput_Password"
                        class="block text-sm font-medium text-gray-700 mb-1">Password</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                            </svg>
                        </div>
                        <input
                            class="appearance-none border rounded-md w-full py-2 focus:ring-2 focus:ring-emerald-400 pl-10 pr-16 leading-tight border-gray-300 bg-gray-50 focus:outline-none focus:border-emerald-400 focus:bg-white text-gray-700 js-password"
                            asp-for="LoginInput.Password" type="password" placeholder="Enter your password" />
                        <div class="absolute inset-y-0 right-0 flex items-center px-2">
                            <input class="hidden js-password-toggle" id="toggle" type="checkbox" />
                            <label
                                class="bg-gray-200 hover:bg-gray-300 rounded px-2 py-1 text-xs text-gray-600 cursor-pointer js-password-label"
                                for="toggle">show</label>
                        </div>
                    </div>
                    <span asp-validation-for="LoginInput.Password" class="text-red-500 text-sm"></span>
                </div>

                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <input asp-for="LoginInput.RememberMe"
                            class="h-4 w-4 rounded border-gray-300 text-emerald-600 focus:ring-emerald-500" />
                        <label asp-for="LoginInput.RememberMe" class="ml-2 block text-sm text-gray-700">Remember
                            me</label>
                    </div>
                </div>

                <div>
                    <button type="submit" id="loginButton"
                        class="w-full inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-emerald-400 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-emerald-600 h-11 px-6 py-3 bg-[#20bf6b] text-white shadow-md">
                        <span id="loginText">Login</span>
                        <svg id="loadingSpinner" class="hidden animate-spin h-5 w-5 ml-2"
                            xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4">
                            </circle>
                            <path class="opacity-75" fill="currentColor"
                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                            </path>
                        </svg>
                    </button>
                </div>
            </form>

            <!-- SSO Login Button -->
            <div class="mt-4 relative">
                <div class="absolute inset-0 flex items-center">
                    <div class="w-full border-t border-gray-300"></div>
                </div>
                <div class="relative flex justify-center text-sm">
                    <span class="px-2 bg-white text-gray-500">Or</span>
                </div>
            </div>

            <div class="mt-4">
                <a href="/Account/ExternalLogin?provider=oidc&returnUrl=@Model.ReturnUrl"
                   class="w-full inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-400 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-blue-600 h-11 px-6 py-3 bg-blue-500 text-white shadow-md">
                    <svg class="h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M9 12l2 2 4-4"></path>
                        <path d="M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 01-1.043 3.296 3.745 3.745 0 01-3.296 1.043A3.745 3.745 0 0112 21c-1.268 0-2.39-.63-3.068-1.593a3.745 3.745 0 01-3.296-1.043 3.745 3.745 0 01-1.043-3.296A3.745 3.745 0 013 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 011.043-3.296 3.745 3.745 0 013.296-1.043A3.745 3.745 0 0112 3c1.268 0 2.39.63 3.068 1.593a3.745 3.745 0 013.296 1.043 3.745 3.745 0 011.043 3.296A3.745 3.745 0 0121 12z"></path>
                    </svg>
                    Login with SSO
                </a>
            </div>

            @if (ViewData["IsActiveDirectoryEnabled"] != null && (bool)ViewData["IsActiveDirectoryEnabled"])
            {
                <!-- Windows Authentication Button -->
                <div class="mt-4 relative">
                    <div class="absolute inset-0 flex items-center">
                        <div class="w-full border-t border-gray-300"></div>
                    </div>
                    <div class="relative flex justify-center text-sm">
                        <span class="px-2 bg-white text-gray-500">Or</span>
                    </div>
                </div>

                <form method="post" asp-page-handler="WindowsLogin" class="mt-4 space-y-5" id="adLoginForm" onsubmit="return handleAdLogin(event)">
                    <input type="hidden" name="ReturnUrl" value="@Model.ReturnUrl" />
                    <input type="hidden" name="ReturnUrlHash" value="@Model.ReturnUrlHash" />

                    <div class="flex items-center">
                        <input asp-for="AdLoginInput.RememberMe" class="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
                        <label asp-for="AdLoginInput.RememberMe" class="ml-2 block text-sm text-gray-700">Remember me</label>
                    </div>

                    <button type="submit"
                            id="adLoginButton"
                            class="w-full inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-400 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-blue-600 h-11 px-6 py-3 bg-blue-500 text-white shadow-md">
                        <svg class="h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <rect x="2" y="2" width="20" height="20" rx="2" ry="2"></rect>
                            <path d="M12 2v20M2 12h20"></path>
                        </svg>
                        <span id="adLoginText">Login with Active Directory</span>
                        <svg id="adLoadingSpinner" class="hidden animate-spin h-5 w-5 ml-2"
                             xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4">
                            </circle>
                            <path class="opacity-75" fill="currentColor"
                                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                            </path>
                        </svg>
                    </button>
                </form>
            }

            <div class="mt-8 text-center">
                <p class="text-sm text-gray-600">
                    If you have problem in login in, please contact our IT Application Development Team: 737
                </p>
                <p class="text-xs text-gray-500 mt-4">
                    &copy; <span id="year"></span> INFORMATION TECHNOLOGY - PT INDONESIA MOROWALI INDUSTRIAL PARK. All rights reserved.
                </p>
            </div>
        </div>
    </div>
</div>

@section scripts {
    <script>
        document.getElementById('year').textContent = new Date().getFullYear();

        // Password toggle functionality
        const passwordToggle = document.querySelector('.js-password-toggle');
        const password = document.querySelector('.js-password');
        const passwordLabel = document.querySelector('.js-password-label');

        passwordToggle.addEventListener('change', function () {
            if (password.type === 'password') {
                password.type = 'text';
                passwordLabel.textContent = 'hide';
            } else {
                password.type = 'password';
                passwordLabel.textContent = 'show';
            }
        });

        // Login form submission handler
        function handleLogin(event) {
            const form = document.getElementById('loginForm');
            const button = document.getElementById('loginButton');
            const spinner = document.getElementById('loadingSpinner');
            const loginText = document.getElementById('loginText');

            // Basic form validation
            if (!form.checkValidity()) {
                return true; // Let the browser handle invalid form
            }

            button.disabled = true;
            spinner.classList.remove('hidden');
            loginText.classList.add('mr-2');

            return true; // Allow form submission to proceed
        }

        // Active Directory login form submission handler
        function handleAdLogin(event) {
            const form = document.getElementById('adLoginForm');
            const button = document.getElementById('adLoginButton');
            const spinner = document.getElementById('adLoadingSpinner');
            const loginText = document.getElementById('adLoginText');

            // Basic form validation
            if (!form.checkValidity()) {
                return true; // Let the browser handle invalid form
            }

            // Show loading state
            button.disabled = true;
            spinner.classList.remove('hidden');
            loginText.classList.add('mr-2');

            // Try to detect Windows authentication
            // This is a simplified approach - in a real implementation,
            // you might use more sophisticated techniques

            // For demonstration, we'll set a cookie that the server can read
            // In a real implementation, you might use SPNEGO/Kerberos or other methods

            // Try to get the current domain username if available
            let windowsUser = '';

            // Try to get from browser environment
            // Note: This is not reliable and will only work in specific environments
            if (typeof window.external !== 'undefined' &&
                typeof window.external.GetUserInfo === 'function') {
                try {
                    windowsUser = window.external.GetUserInfo();
                } catch (e) {
                    console.log('Could not get Windows user info');
                }
            }

            // Set a cookie with the Windows username (if available)
            if (windowsUser) {
                document.cookie = `WindowsAuthUser=${windowsUser}; path=/; secure; samesite=strict`;
            }

            // Continue with form submission
            return true; // Allow form submission to proceed
        }
    </script>
}