'use client'
import { type CreateUpdateClaimDto, postApiIdentityClaims } from '@/client'
import { Button } from '@/components/ui/button'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { useToast } from '@/lib/useToast'
import { QueryNames } from '@/lib/hooks/QueryConstants'
import { useGrantedPolicies } from '@/lib/hooks/useGrantedPolicies'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import { Toaster } from '@/components/ui/toaster'
import { RiAddLine } from '@remixicon/react'
import { FormField, FormSection } from '@/components/ui/FormField'
import { handleApiError } from '@/lib/handleApiError'

export type AddClientProps = {
  children?: React.ReactNode
}

export const Add = ({ children }: AddClientProps) => {
  const { can } = useGrantedPolicies()
  const [open, setOpen] = useState(false)
  const { toast } = useToast()
  const queryClient = useQueryClient()
  const { handleSubmit, register } = useForm<CreateUpdateClaimDto>()

  const createDataMutation = useMutation({
    mutationFn: async (formData: CreateUpdateClaimDto) =>
      postApiIdentityClaims({
        body: formData
      }),
    onSuccess: () => {
      toast({
        title: 'Success',
        description: 'Resource Created Successfully',
        variant: 'success',
      })
      void queryClient.invalidateQueries({ queryKey: [QueryNames.GetOpeniddictResources] })
      setOpen(false)
    },
    onError: (err: unknown) => {
      console.log('Error creating client:', err);
      const error = handleApiError(err);
      toast({
        title: error.title,
        description: error.description,
        variant: 'error',
      })
    }
  })

  const onSubmit = (formData: CreateUpdateClaimDto) => {
    // Merge form data with consent type and permissions
    const userData: CreateUpdateClaimDto = {
      ...formData,
    }

    createDataMutation.mutate(userData)
  }

  return (
    <section>
      <Toaster />
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger asChild>{children}</DialogTrigger>
        <section className="flex items-center justify-between pb-5">
          {can('AbpIdentity.Users.Create') && (
            <Button size='sm' className="w-full sm:py-1 sm:mt-0 sm:w-fit" onClick={() => setOpen(true)}>
              <RiAddLine className="-ml-1 size-4 shrink-0" aria-hidden="true" />
              <span className="hidden truncate sm:inline">New Claim</span>
            </Button>
          )}
        </section>
        <DialogContent size='xl'>
          <DialogHeader>
            <DialogTitle>Create a New Claim</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleSubmit(onSubmit)} className='mt-2'>
            <section className="flex w-full flex-col space-y-2">
              <FormSection>
                <FormField
                  label="Claim Type"
                  description="The unique identifier for this claim. Used in requests"
                >
                  <Input required {...register('claimType')} placeholder="Claim Type" />
                </FormField>
                <FormField
                  label="Claim Value"
                  description="The value for this claim"
                >
                  <Input required {...register('claimValue')} placeholder="Claim Value" />
                </FormField>
              </FormSection>
            </section>
            <DialogFooter className="mt-5">
              <Button
                variant="ghost"
                onClick={(e: React.MouseEvent) => {
                  e.preventDefault()
                  setOpen(false)
                }}
                disabled={createDataMutation.isPending}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={createDataMutation.isPending}>
                {createDataMutation.isPending ? 'Saving...' : 'Save'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </section>
  )
}
