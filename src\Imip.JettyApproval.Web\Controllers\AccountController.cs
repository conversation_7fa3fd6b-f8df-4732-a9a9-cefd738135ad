using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace Imip.JettyApproval.Web.Controllers
{
    [Route("[controller]/[action]")]
    public class AccountController : Controller
    {
        [HttpGet]
        public IActionResult ExternalLogin(string provider, string returnUrl = null)
        {
            // Request a redirect to the external login provider
            var redirectUrl = Url.Action(nameof(ExternalLoginCallback), "Account", new { returnUrl });
            var properties = new AuthenticationProperties { RedirectUri = redirectUrl };
            
            return Challenge(properties, provider);
        }

        [HttpGet]
        public async Task<IActionResult> ExternalLoginCallback(string returnUrl = null, string remoteError = null)
        {
            if (remoteError != null)
            {
                // Handle error from external provider
                TempData["ErrorMessage"] = $"Error from external provider: {remoteError}";
                return RedirectToPage("/Account/Login");
            }

            // The user should be authenticated at this point
            if (User.Identity.IsAuthenticated)
            {
                // Redirect to the return URL or home page
                if (!string.IsNullOrEmpty(returnUrl) && Url.IsLocalUrl(returnUrl))
                {
                    return Redirect(returnUrl);
                }
                return RedirectToAction("Index", "Home");
            }

            // If we got this far, something failed
            TempData["ErrorMessage"] = "Unable to authenticate with external provider.";
            return RedirectToPage("/Account/Login");
        }

        [HttpPost]
        [Authorize]
        public async Task<IActionResult> Logout()
        {
            // Sign out from both the application and the external provider
            await HttpContext.SignOutAsync("Cookies");
            await HttpContext.SignOutAsync("oidc");
            
            return RedirectToPage("/Account/Login");
        }
    }
}
